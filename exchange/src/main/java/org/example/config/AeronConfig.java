package org.example.config;

import io.aeron.Aeron;
import io.aeron.driver.MediaDriver;
import io.aeron.driver.ThreadingMode;
import org.agrona.concurrent.BusySpinIdleStrategy;
import org.agrona.concurrent.IdleStrategy;
import org.agrona.concurrent.NoOpIdleStrategy;
import org.agrona.concurrent.SleepingIdleStrategy;

public class AeronConfig {
    // Channels and stream IDs
    public static final String COMMAND_CHANNEL = "aeron:ipc";
    public static final int COMMAND_STREAM_ID = 10;
    public static final String EVENT_CHANNEL = "aeron:ipc";
    public static final int EVENT_STREAM_ID = 20;
    public static final String MEDIA_DRIVER_DIRECTORY = "/Users/<USER>/core/media-driver";
    public static final String ARCHIVE_DIRECTORY = "/Users/<USER>/core/archive";
    public static final IdleStrategy IDLE_STRATEGY = new SleepingIdleStrategy();
    public static final String CONTROL_REQUEST_CHANNEL = "aeron:udp?endpoint=localhost:8010";
    public static final String CONTROL_RESPONSE_CHANNEL = "aeron:udp?endpoint=localhost:0";
    public static final long EVENT_RECORDING_ID = 0L;
    public static final long SNAPSHOT_RECORD_ID = 1L;
    // Fragment limits
    public static final int FRAGMENT_LIMIT = 1;

    // Idle strategies
    public static final IdleStrategy PUBLISHER_IDLE_STRATEGY = new BusySpinIdleStrategy();
    public static final IdleStrategy SUBSCRIBER_IDLE_STRATEGY = new BusySpinIdleStrategy();
    public static final IdleStrategy AGENT_IDLE_STRATEGY = new BusySpinIdleStrategy();

    // Media Driver configuration
    public static MediaDriver.Context createMediaDriverContext() {
        return new MediaDriver.Context()
                .threadingMode(ThreadingMode.SHARED)
                .dirDeleteOnStart(true)
                .dirDeleteOnShutdown(true)
                .termBufferSparseFile(false)
                .conductorIdleStrategy(new NoOpIdleStrategy())
                .receiverIdleStrategy(new NoOpIdleStrategy())
                .senderIdleStrategy(new NoOpIdleStrategy());
    }

    // Aeron client configuration
    public static Aeron.Context createAeronContext() {
        return new Aeron.Context()
                .idleStrategy(new BusySpinIdleStrategy());
    }
}