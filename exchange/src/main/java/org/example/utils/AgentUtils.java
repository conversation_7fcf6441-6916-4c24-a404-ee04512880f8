package org.example.utils;

import org.agrona.concurrent.Agent;
import org.agrona.concurrent.AgentRunner;

import static org.example.config.AeronConfig.IDLE_STRATEGY;

public class AgentUtils {

    public static void startAgent(Agent agent) {
        final AgentRunner runner = new AgentRunner
                (IDLE_STRATEGY,
                        Throwable::printStackTrace,
                        null,
                        agent);
        AgentRunner.startOnThread(runner);
    }

}
