package org.example.sbe;

import org.agrona.DirectBuffer;
import org.agrona.MutableDirectBuffer;

/**
 * Common interface for all SBE message encoders to enable polymorphic handling.
 * This interface provides standard methods for message encoding and metadata access.
 */
public interface MessageFlyweight {
    
    /**
     * Get the template ID for this message type.
     * @return the SBE template ID
     */
    int sbeTemplateId();
    
    /**
     * Get the schema ID for this message.
     * @return the SBE schema ID
     */
    int sbeSchemaId();
    
    /**
     * Get the schema version for this message.
     * @return the SBE schema version
     */
    int sbeSchemaVersion();
    
    /**
     * Get the block length (fixed portion) of this message.
     * @return the block length in bytes
     */
    int sbeBlockLength();
    
    /**
     * Get the total encoded length of this message including variable portions.
     * @return the total encoded length in bytes
     */
    int encodedLength();
    
    /**
     * Get the underlying buffer that this encoder is wrapping.
     * @return the buffer containing the encoded message
     */
    MutableDirectBuffer buffer();
    
    /**
     * Get the offset within the buffer where this message starts.
     * @return the offset in bytes
     */
    int offset();
    
    /**
     * Get the limit (end position) of this message in the buffer.
     * @return the limit position in bytes
     */
    int limit();
    
    /**
     * Reset this encoder to its initial state.
     * @return this encoder for method chaining
     */
    MessageFlyweight reset();
    
    /**
     * Wrap a buffer for encoding, starting at the specified offset.
     * @param buffer the buffer to wrap
     * @param offset the starting offset
     * @return this encoder for method chaining
     */
    MessageFlyweight wrap(MutableDirectBuffer buffer, int offset);
}
