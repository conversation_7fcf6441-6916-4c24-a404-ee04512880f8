package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.*;

import java.nio.ByteBuffer;

import static org.example.config.AeronConfig.*;

public class PositionNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
    private final OrderEncoder orderEncoder = new OrderEncoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer(ByteBuffer.allocate(256));

    Aeron aeron;
    Subscription eventStreamSubscription;
    Publication commandStreamPublication;
    PositionNodeAppStatus sequencerStatus = PositionNodeAppStatus.INIT;

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().driverTimeoutMs(20_000).aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.eventStreamSubscription = aeron.addSubscription(EVENT_CHANNEL, EVENT_STREAM_ID);
        this.commandStreamPublication = aeron.addPublication(COMMAND_CHANNEL, COMMAND_STREAM_ID);
    }

    @Override
    public int doWork() throws Exception {

        switch (sequencerStatus) {
            case INIT:
                return checkConnectionStatus();
            case RUNNING:
                return publishOrderEvent() + pullSequenceEvent();
            default:
                return 0;
        }
    }

    private int pullSequenceEvent() {
        return eventStreamSubscription.poll(fragmentAssembler, FRAGMENT_LIMIT);
    }

    long price = 1L;

    private int publishOrderEvent() {
        orderEncoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderEncoder);
        orderEncoder.orderType(OrderType.LIMIT);
        orderEncoder.accountMsb(1L);
        orderEncoder.accountLsb(1L);
        orderEncoder.quantity(1);
        orderEncoder.price(price++);
        long result = commandStreamPublication.offer(unsafeBuffer, 0, MessageHeaderEncoder.ENCODED_LENGTH + orderEncoder.encodedLength());
        return 1;
    }

    private int checkConnectionStatus() {
        System.out.println("check connection status");
        if (eventStreamSubscription.isConnected() && commandStreamPublication.isConnected()) {
            sequencerStatus = PositionNodeAppStatus.RUNNING;
            return 1;
        } else {
            return 0;
        }
    }


    int index = 0;

    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
        long sequence = messageHeaderDecoder.sequence();
    }


    @Override
    public void onClose() {
        Agent.super.onClose();
    }

    @Override
    public String roleName() {
        return "";
    }


    enum PositionNodeAppStatus {
        RUNNING, INIT
    }

}
