package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.archive.client.AeronArchive;
import io.aeron.archive.codecs.SourceLocation;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.collections.Int2ObjectHashMap;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.*;

import java.nio.ByteBuffer;

import static org.example.config.AeronConfig.*;

public class PositionNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
    private final OrderEncoder orderEncoder = new OrderEncoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer(ByteBuffer.allocate(256));

    Aeron aeron;
    AeronArchive aeronArchive;
    Subscription eventStreamSubscription;
    Publication commandStreamPublication;
    PositionNodeAppStatus sequencerStatus = PositionNodeAppStatus.INIT;
    Int2ObjectHashMap<Integer> value;

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        //  this.aeronForArchive = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.eventStreamSubscription = aeron.addSubscription(IPC_CHANNEL, EVENT_STREAM_ID);
        this.commandStreamPublication = aeron.addPublication(IPC_CHANNEL, COMMAND_STREAM_ID);
    }

    @Override
    public int doWork() throws Exception {

        switch (sequencerStatus) {
            case INIT:
                return checkConnectionStatus();
            case RUNNING:
                return publishOrderEvent() + pullSequenceEvent();
            default:
                return 0;
        }
    }

    private int pullSequenceEvent() {
        return eventStreamSubscription.poll(fragmentAssembler, FRAGMENT_LIMIT);
    }

    long price = 1L;
    long latestPosition = 0L;

    /*
    *
    *
    *        <field name="orderType" id="1" type="OrderType"/>
        <field name="accountMsb" id="2" type="uint64"/>
        <field name="accountLsb" id="3" type="uint64"/>
        <field name="quantity" id="4" type="uint64"/>
        <field name="price" id="5" type="uint64"/>
    *
    * */

    long quantity = 2000L;

    long orderPrice = 1L;

    private int publishOrderEvent() {
        orderEncoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderEncoder);
        orderEncoder.orderType(OrderType.LIMIT);
        orderEncoder.accountMsb(1L);
        orderEncoder.accountLsb(2L);
        orderEncoder.quantity(quantity++);
        orderEncoder.price(orderPrice++);
        long result = commandStreamPublication.offer(unsafeBuffer, 0, MessageHeaderEncoder.ENCODED_LENGTH + orderEncoder.encodedLength());

        latestPosition = result > 0 ? result : latestPosition;
//        if (result < 0) {
//            System.out.println("the last successfully publish position is : " + latestPosition);
//        }


        return result < 0 ? 0 : 1;
    }

    private int checkConnectionStatus() {
        System.out.println("check connection status");
        if (eventStreamSubscription.isConnected() && commandStreamPublication.isConnected()) {
            sequencerStatus = PositionNodeAppStatus.RUNNING;
            this.aeronArchive = AeronArchive.connect(
                    new AeronArchive.Context()
                            .aeron(aeron)
                            .controlRequestChannel(CONTROL_REQUEST_CHANNEL)
                            .controlResponseChannel(CONTROL_RESPONSE_CHANNEL)
            );
            aeronArchive.startRecording(IPC_CHANNEL, EVENT_STREAM_ID, SourceLocation.LOCAL);
            System.out.println("archive session id " + aeronArchive.controlSessionId());
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
        long sequence = messageHeaderDecoder.sequence();
    }


    @Override
    public void onClose() {
        aeronArchive.close();
        aeron.close();
    }

    @Override
    public String roleName() {
        return "";
    }


    enum PositionNodeAppStatus {
        RUNNING, INIT
    }

}
