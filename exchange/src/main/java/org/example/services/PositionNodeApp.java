package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.models.NodeAppStatus;
import org.example.sbe.*;

import java.nio.ByteBuffer;

import static org.example.config.AeronConfig.*;
import static org.example.models.NodeAppStatus.INIT;
import static org.example.models.NodeAppStatus.RUNNING;

public class PositionNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final OrderEncoder orderEncoder = new OrderEncoder();
    private final ExecutionReportEncoder executionReportEncoder = new ExecutionReportEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer(ByteBuffer.allocate(2048));

    Aeron aeron;
    Subscription eventStreamSubscription;
    Publication commandStreamPublication;
    NodeAppStatus nodeAppStatus = INIT;

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.eventStreamSubscription = aeron.addSubscription(IPC_CHANNEL, EVENT_STREAM_ID);
        this.commandStreamPublication = aeron.addPublication(IPC_CHANNEL, COMMAND_STREAM_ID);
    }

    @Override
    public int doWork() {

        switch (nodeAppStatus) {
            case INIT:
                return checkConnectionStatus();
            case RUNNING:
                return publishOrderEvent() + pullSequenceEvent() + publishExecutionReportEvent();
            default:
                return 0;
        }
    }

    private int pullSequenceEvent() {
        return eventStreamSubscription.poll(fragmentAssembler, FRAGMENT_LIMIT);
    }

    long latestPosition = 0L;
    long quantity = 2000L;
    long orderPrice = 1L;

    private int publishOrderEvent() {
        orderEncoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderEncoder);
        orderEncoder.orderType(OrderType.LIMIT);
        orderEncoder.accountMsb(1L);
        orderEncoder.accountLsb(2L);
        orderEncoder.quantity(quantity++);
        orderEncoder.price(orderPrice++);
        long result = commandStreamPublication.offer(unsafeBuffer, 0, MessageHeaderEncoder.ENCODED_LENGTH + orderEncoder.encodedLength());
        latestPosition = result > 0 ? result : latestPosition;
        return result < 0 ? 0 : 1;
    }

    long tradeId = 1L;
    long tradePrice = 1000L;

    public int publishExecutionReportEvent() {
        executionReportEncoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderEncoder);

        // Set ALL required fields
        executionReportEncoder.executionId(1L);
        executionReportEncoder.orderId(2L);
        executionReportEncoder.executionType(ExecutionType.PARTIAL_FILL);
//        executionReportEncoder.executionStatus(ExecutionStatus.EXECUTED);
        executionReportEncoder.timestamp(System.nanoTime());
//        executionReportEncoder.symbol("TESTINST"); // 8 characters
        executionReportEncoder.side(Side.BUY);
        executionReportEncoder.totalQuantity(200L);
        executionReportEncoder.executedQuantity(100L);
        executionReportEncoder.remainingQuantity(100L);
        executionReportEncoder.avgPrice(1000L);

        // Add trades
        ExecutionReportEncoder.TradesEncoder tradesEncoder = executionReportEncoder.tradesCount(2);
        for (int index = 0; index < 2; index++) {
            tradesEncoder.next();
            tradesEncoder.tradeId(123L + tradeId);
            tradesEncoder.tradePrice(100L + tradePrice);
            tradesEncoder.tradeQuantity(50L); // Add missing fields
            tradesEncoder.tradeTimestamp(System.nanoTime());
            tradesEncoder.counterpartyId(999L);
        }

        // Use the CORRECT length calculation
        int totalLength = MessageHeaderEncoder.ENCODED_LENGTH + executionReportEncoder.encodedLength();
        long result = commandStreamPublication.offer(unsafeBuffer, 0, totalLength);

        latestPosition = result > 0 ? result : latestPosition;
        return result < 0 ? 0 : 1;
    }

    private int checkConnectionStatus() {
        if (eventStreamSubscription.isConnected() && commandStreamPublication.isConnected()) {
            nodeAppStatus = RUNNING;
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
    }

    @Override
    public void onClose() {
        aeron.close();
    }

    @Override
    public String roleName() {
        return "PositionNodeApp";
    }


}
