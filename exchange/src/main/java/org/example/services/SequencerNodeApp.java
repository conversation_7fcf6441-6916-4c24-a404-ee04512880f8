package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.ExpandableArrayBuffer;
import org.agrona.MutableDirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.MessageHeaderDecoder;
import org.example.sbe.MessageHeaderEncoder;
import org.example.sbe.OrderDecoder;
import org.example.sbe.OrderEncoder;

import static org.example.config.AeronConfig.*;


public class SequencerNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final OrderDecoder orderDecoder = new OrderDecoder();


    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer();
    private long sequence = 1L;
    Aeron aeron;
    Subscription commandStreamSubscription;
    Publication eventStreamPublication;
    SequencerStatus sequencerStatus = SequencerStatus.INIT;

    public static void main(String[] args) {
        final MutableDirectBuffer buffer = new ExpandableArrayBuffer(1024 * 4);
        final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
        final OrderEncoder orderEncoder = new OrderEncoder();

        final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
        final OrderDecoder orderDecoder = new OrderDecoder();

        orderEncoder.wrapAndApplyHeader(buffer, 0, messageHeaderEncoder);
        orderEncoder.accountMsb(9L);
        orderEncoder.accountLsb(12L);
        orderDecoder.wrapAndApplyHeader(buffer, 0, messageHeaderDecoder);
        System.out.println("the account msb " + orderDecoder.accountMsb());
        System.out.println("the account lsb " + orderDecoder.accountLsb());

    }

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.commandStreamSubscription = aeron.addSubscription(IPC_CHANNEL, COMMAND_STREAM_ID);
        this.eventStreamPublication = aeron.addPublication(IPC_CHANNEL, EVENT_STREAM_ID);
    }

    @Override
    public int doWork() {
        return switch (sequencerStatus) {
            case INIT -> checkConnectionStatus();
            case RUNNING -> pollCommandEvent();
        };
    }

    private int checkConnectionStatus() {
        if (commandStreamSubscription.isConnected() && eventStreamPublication.isConnected()) {
            sequencerStatus = SequencerStatus.RUNNING;

            return 1;
        } else {
            return 0;
        }
    }

    private int pollCommandEvent() {

        int pollCount = commandStreamSubscription.poll(fragmentAssembler, 1); // Increased from 1 to 10
        return pollCount;
    }


    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
        unsafeBuffer.wrap(buffer);
        messageHeaderEncoder.wrap(unsafeBuffer, offset);
        switch (messageHeaderDecoder.templateId()) {
            case OrderDecoder.TEMPLATE_ID:
                orderDecoder.wrap(buffer, offset + messageHeaderDecoder.encodedLength(), messageHeaderDecoder.blockLength(), messageHeaderDecoder.version());
                messageHeaderEncoder.sequence(sequence++);
                long result = eventStreamPublication.offer(unsafeBuffer, offset, length);
                if (result < 0) {
                    sequence--;
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onClose() {
        aeron.close();
    }


    @Override
    public String roleName() {
        return "";
    }

    enum SequencerStatus {
        RUNNING, INIT
    }
}
