package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.models.NodeAppStatus;
import org.example.sbe.MessageHeaderDecoder;
import org.example.sbe.MessageHeaderEncoder;
import org.example.sbe.OrderDecoder;

import static org.example.config.AeronConfig.*;

public class SequencerNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final OrderDecoder orderDecoder = new OrderDecoder();
    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer();
    private long aeronEventPosition;
    private long sequence = 1L;
    Aeron aeron;
    Subscription commandStreamSubscription;
    Publication eventStreamPublication;
    NodeAppStatus sequencerStatus = NodeAppStatus.INIT;

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.commandStreamSubscription = aeron.addSubscription(IPC_CHANNEL, COMMAND_STREAM_ID);
        this.eventStreamPublication = aeron.addPublication(IPC_CHANNEL, EVENT_STREAM_ID);
    }

    @Override
    public int doWork() {
        return switch (sequencerStatus) {
            case INIT -> checkConnectionStatus();
            case RUNNING -> pollCommandEvent();
        };
    }

    private int checkConnectionStatus() {
        if (commandStreamSubscription.isConnected() && eventStreamPublication.isConnected()) {
            sequencerStatus = NodeAppStatus.RUNNING;

            return 1;
        } else {
            return 0;
        }
    }

    private int pollCommandEvent() {
        return commandStreamSubscription.poll(fragmentAssembler, 1);
    }

    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
        unsafeBuffer.wrap(buffer);
        messageHeaderEncoder.wrap(unsafeBuffer, offset);
        switch (messageHeaderDecoder.templateId()) {
            case OrderDecoder.TEMPLATE_ID:
                //orderDecoder.wrap(buffer, offset + messageHeaderDecoder.encodedLength(), messageHeaderDecoder.blockLength(), messageHeaderDecoder.version());
                messageHeaderEncoder.sequence(sequence++);
                long result = eventStreamPublication.offer(unsafeBuffer, offset, length);
                if (result < 0) {
                    sequence--;
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onClose() {
        aeron.close();
    }


    @Override
    public String roleName() {
        return "SequencerNodeApp";
    }


}
