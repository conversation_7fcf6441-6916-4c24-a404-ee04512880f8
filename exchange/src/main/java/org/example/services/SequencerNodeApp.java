package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.MessageHeaderDecoder;
import org.example.sbe.MessageHeaderEncoder;
import org.example.sbe.OrderDecoder;

import static org.example.config.AeronConfig.*;

public class SequencerNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final OrderDecoder orderDecoder = new OrderDecoder();
    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer();
    private long sequence = 1L;
    Aeron aeron;
    Subscription commandStreamSubscription;
    Publication eventStreamPublication;
    SequencerStatus sequencerStatus = SequencerStatus.INIT;

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.commandStreamSubscription = aeron.addSubscription(COMMAND_CHANNEL, COMMAND_STREAM_ID);
        this.eventStreamPublication = aeron.addPublication(EVENT_CHANNEL, EVENT_STREAM_ID);
    }

    @Override
    public int doWork() {
        return switch (sequencerStatus) {
            case INIT -> checkConnectionStatus();
            case RUNNING -> pollCommandEvent();
        };
    }

    private int checkConnectionStatus() {
        if (commandStreamSubscription.isConnected() && eventStreamPublication.isConnected()) {
            sequencerStatus = SequencerStatus.RUNNING;
            return 1;
        } else {
            return 0;
        }
    }

    private int pollCommandEvent() {
        // Process more messages per cycle to reduce back pressure
        int pollCount = commandStreamSubscription.poll(fragmentAssembler, 10); // Increased from 1 to 10
        return pollCount;
    }


    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
        unsafeBuffer.wrap(buffer);
        messageHeaderEncoder.wrap(unsafeBuffer, offset);
        switch (messageHeaderDecoder.templateId()) {
            case OrderDecoder.TEMPLATE_ID:
                orderDecoder.wrap(buffer, offset + messageHeaderDecoder.encodedLength(), messageHeaderDecoder.blockLength(), messageHeaderDecoder.version());
                messageHeaderEncoder.sequence(sequence++);
                eventStreamPublication.offer(unsafeBuffer, offset, length);
                break;
            default:
                break;
        }

    }


    @Override
    public void onClose() {
        Agent.super.onClose();
    }

    @Override
    public String roleName() {
        return "";
    }

    enum SequencerStatus {
        RUNNING, INIT
    }
}
