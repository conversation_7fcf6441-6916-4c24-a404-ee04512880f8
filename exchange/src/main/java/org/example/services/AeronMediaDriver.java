package org.example.services;

import io.aeron.archive.Archive;
import io.aeron.archive.ArchiveThreadingMode;
import io.aeron.archive.ArchivingMediaDriver;
import io.aeron.driver.MediaDriver;
import io.aeron.driver.ThreadingMode;
import org.agrona.concurrent.Agent;

import java.time.Duration;

import static org.example.config.AeronConfig.*;

public class AeronMediaDriver implements Agent {


    @Override
    public void onStart() {
        MediaDriver mediaDriver = MediaDriver.launch(new MediaDriver
                .Context()
                .threadingMode(ThreadingMode.DEDICATED)
                .aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));

//        long nanos = Duration.ofSeconds(20_000).toNanos();
//        ArchivingMediaDriver archivingMediaDriver = ArchivingMediaDriver.launch(
//                new MediaDriver.Context()
//                        .threadingMode(ThreadingMode.DEDICATED)
//                        .aeronDirectoryName(MEDIA_DRIVER_DIRECTORY)
//                        .spiesSimulateConnection(true)
//                        .dirDeleteOnStart(true),
//                new Archive.Context()
//                        .threadingMode(ArchiveThreadingMode.DEDICATED)
//                        .deleteArchiveOnStart(true)
//                        .controlChannel(CONTROL_REQUEST_CHANNEL)
//                        .replicationChannel(REPLICATION_CHANNEL)
//                        .archiveDirectoryName(ARCHIVE_DIRECTORY)
//        );

    }

    @Override
    public int doWork() {
        return 0;
    }

    @Override
    public void onClose() {
        System.out.println("media driver is closing");
    }

    @Override
    public String roleName() {
        return "";
    }
}
