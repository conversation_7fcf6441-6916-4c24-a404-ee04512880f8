package org.example.services;

import io.aeron.archive.Archive;
import io.aeron.driver.MediaDriver;
import org.agrona.concurrent.Agent;

import static org.example.config.AeronConfig.*;

public class AeronMediaDriver implements Agent {

    MediaDriver driver;
    Archive archive;

    @Override
    public void onStart() {

        driver = MediaDriver.launch(MediaDriverContext);
        archive = Archive.launch(ArchiveContext);

    }

    @Override
    public int doWork() {
        return 0;
    }

    @Override
    public void onClose() {
        archive.close();
        driver.close();
    }

    @Override
    public String roleName() {
        return "";
    }
}
