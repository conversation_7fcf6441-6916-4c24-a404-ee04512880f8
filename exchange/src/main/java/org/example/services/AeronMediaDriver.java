package org.example.services;

import io.aeron.archive.Archive;
import io.aeron.archive.ArchiveThreadingMode;
import io.aeron.archive.ArchivingMediaDriver;
import io.aeron.driver.MediaDriver;
import io.aeron.driver.ThreadingMode;
import io.aeron.driver.status.SystemCounterDescriptor;
import org.agrona.ErrorHandler;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.status.AtomicCounter;

import static org.example.config.AeronConfig.*;

public class AeronMediaDriver implements Agent {


    @Override
    public void onStart() {


        final MediaDriver.Context mediaDriverContext = new MediaDriver.Context()
                .threadingMode(ThreadingMode.SHARED)
                .aeronDirectoryName(MEDIA_DRIVER_DIRECTORY)
                .spiesSimulateConnection(true)
                .dirDeleteOnStart(true);

        final Archive.Context archiveContext = new Archive.Context()
                .threadingMode(ArchiveThreadingMode.INVOKER)
                .deleteArchiveOnStart(true)
                .controlChannel(CONTROL_REQUEST_CHANNEL)
                .replicationChannel(REPLICATION_CHANNEL)
                .archiveDirectoryName(ARCHIVE_DIRECTORY)
                .aeronDirectoryName(MEDIA_DRIVER_DIRECTORY);


        MediaDriver driver = MediaDriver.launch(mediaDriverContext);
        Archive archive = Archive.launch(archiveContext);


    }

    @Override
    public int doWork() {
        return 0;
    }

    @Override
    public void onClose() {
        System.out.println("media driver is closing");
    }

    @Override
    public String roleName() {
        return "";
    }
}
