/*
 * This source file was generated by the Gradle 'init' task
 */
package org.example;

import org.example.services.AeronMediaDriver;
import org.example.services.PositionNodeApp;
import org.example.services.SequencerNodeApp;
import org.example.utils.AgentUtils;

public class App {
    public String getGreeting() {
        return "Hello World!";
    }

    public static void main(String[] args) {
        AgentUtils.startAgent(new AeronMediaDriver());
        AgentUtils.startAgent(new SequencerNodeApp());
        AgentUtils.startAgent(new PositionNodeApp());
    }
}
