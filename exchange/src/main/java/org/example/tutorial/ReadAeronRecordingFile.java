package org.example.tutorial;

import org.agrona.DirectBuffer;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.MessageHeaderDecoder;
import org.example.sbe.OrderDecoder;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

public class ReadAeronRecordingFile {
    public static void main(String[] args) {
        // Configuration
        String archiveDir = "/Users/<USER>/core/archive";
        String recordingFile = "0-0.rec"; // Replace with actual file name
        int maxBytes = 288;
        int messageSize = 32; // Expected size of Order message

        // SBE decoders
        MessageHeaderDecoder headerDecoder = new MessageHeaderDecoder();
        OrderDecoder orderDecoder = new OrderDecoder();
        DirectBuffer buffer = new UnsafeBuffer(ByteBuffer.allocate(maxBytes));

        // Read file
        Path filePath = Paths.get(archiveDir, recordingFile);
        try (FileChannel channel = FileChannel.open(filePath, StandardOpenOption.READ)) {
            // Read up to maxBytes
            int bytesRead = channel.read(buffer.byteBuffer(), 0);
            if (bytesRead <= 0) {
                System.out.println("File is empty or could not be read");
                return;
            }

            // Print raw bytes
            System.out.printf("First %d bytes of %s:%n", Math.min(bytesRead, maxBytes), recordingFile);
            for (int i = 0; i < Math.min(bytesRead, maxBytes); i++) {
                System.out.printf("%02X ", buffer.getByte(i));
                if ((i + 1) % 32 == 0) System.out.println();
            }
            System.out.println();

            // Attempt to decode messages, assuming minimal header
            int offset = 0; // Start after assumed header (adjust if needed)
            while (offset + messageSize <= bytesRead && offset < maxBytes) {
                try {
                    headerDecoder.wrap(buffer, offset);
                    if (headerDecoder.schemaId() == 1 && headerDecoder.templateId() == OrderDecoder.TEMPLATE_ID) {
                        orderDecoder.wrap(buffer, offset + headerDecoder.encodedLength(),
                                headerDecoder.blockLength(), headerDecoder.version());
                        System.out.printf("Message at offset %d: block length=%d, templateId=%d ,version=%d schemaId=%d, templateId=%d, sequence=%d, " +
                                        "orderType=%s, accountMsb=%d, accountLsb=%d, quantity=%d, price=%d%n",
                                offset, headerDecoder.blockLength(), headerDecoder.templateId(), headerDecoder.version(), headerDecoder.schemaId(), headerDecoder.templateId(), headerDecoder.sequence(),
                                orderDecoder.orderType(), orderDecoder.accountMsb(), orderDecoder.accountLsb(),
                                orderDecoder.quantity(), orderDecoder.price());
                    } else {
                        System.out.printf("Invalid message at offset %d: schemaId=%d, templateId=%d%n",
                                offset, headerDecoder.schemaId(), headerDecoder.templateId());
                    }
                } catch (Exception e) {
                    System.out.printf("Failed to decode at offset %d: %s%n", offset, e.getMessage());
                }
                offset += messageSize; // Move to next message
            }
        } catch (IOException e) {
            System.err.println("Error reading file: " + e.getMessage());
            e.printStackTrace();
        }
    }
}