package org.example.tutorial;


import io.aeron.logbuffer.FrameDescriptor;
import io.aeron.protocol.DataHeaderFlyweight;
import org.agrona.BitUtil;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.MessageHeaderDecoder;
import org.example.sbe.OrderDecoder;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static io.aeron.logbuffer.FrameDescriptor.*;
import static io.aeron.protocol.DataHeaderFlyweight.HEADER_LENGTH;
import static io.aeron.protocol.HeaderFlyweight.HDR_TYPE_DATA;

/**
 * Comprehensive parser for Aeron archive recording segment files.
 * Scans directories for .rec files and extracts all recorded messages with precise offset information.
 */
public class AeronRecordingFileParser {

    private static final String RECORDING_SEGMENT_SUFFIX = ".rec";
    private static final Pattern SEGMENT_FILE_PATTERN = Pattern.compile("(\\d+)-(\\d+)\\.rec");

    /**
     * Represents a parsed message from a recording file.
     */
    public static class ParsedMessage {
        public final String fileName;
        public final long fileOffset;           // Absolute offset in file where frame starts
        public final long payloadOffset;       // Absolute offset in file where payload starts (after header)
        public final int frameLength;          // Total frame length including header
        public final int payloadLength;        // Payload length excluding header
        public final int frameType;            // Frame type (DATA, PAD, etc.)
        public final byte flags;               // Frame flags (BEGIN, END, etc.)
        public final int sessionId;            // Session identifier
        public final int streamId;             // Stream identifier
        public final int termId;               // Term identifier
        public final int termOffset;           // Offset within term
        public final long reservedValue;       // Reserved/user value
        public final byte[] payloadData;       // Raw payload bytes

        public ParsedMessage(String fileName, long fileOffset, long payloadOffset, int frameLength,
                             int payloadLength, int frameType, byte flags, int sessionId, int streamId,
                             int termId, int termOffset, long reservedValue, byte[] payloadData) {
            this.fileName = fileName;
            this.fileOffset = fileOffset;
            this.payloadOffset = payloadOffset;
            this.frameLength = frameLength;
            this.payloadLength = payloadLength;
            this.frameType = frameType;
            this.flags = flags;
            this.sessionId = sessionId;
            this.streamId = streamId;
            this.termId = termId;
            this.termOffset = termOffset;
            this.reservedValue = reservedValue;
            this.payloadData = payloadData;
        }

        @Override
        public String toString() {
            return String.format(
                    "ParsedMessage{file='%s', fileOffset=%d, payloadOffset=%d, frameLength=%d, " +
                            "payloadLength=%d, frameType=%d, flags=0x%02X, sessionId=%d, streamId=%d, " +
                            "termId=%d, termOffset=%d, reservedValue=%d}",
                    fileName, fileOffset, payloadOffset, frameLength, payloadLength, frameType,
                    flags, sessionId, streamId, termId, termOffset, reservedValue);
        }

        public String getFlagsDescription() {
            StringBuilder sb = new StringBuilder();
            if ((flags & BEGIN_FRAG_FLAG) != 0) sb.append("BEGIN ");
            if ((flags & END_FRAG_FLAG) != 0) sb.append("END ");
            if ((flags & UNFRAGMENTED) == UNFRAGMENTED) sb.append("UNFRAGMENTED ");
            return sb.toString().trim();
        }
    }

    /**
     * Represents a recording segment file with parsed metadata.
     */
    public static class SegmentFileInfo {
        public final File file;
        public final long recordingId;
        public final long segmentBasePosition;

        public SegmentFileInfo(File file, long recordingId, long segmentBasePosition) {
            this.file = file;
            this.recordingId = recordingId;
            this.segmentBasePosition = segmentBasePosition;
        }

        @Override
        public String toString() {
            return String.format("SegmentFile{file='%s', recordingId=%d, segmentBasePosition=%d}",
                    file.getName(), recordingId, segmentBasePosition);
        }
    }

    /**
     * Scans a directory for Aeron recording segment files (.rec extension).
     *
     * @param directoryPath Path to the directory containing recording files
     * @return List of segment file information
     * @throws IOException if directory cannot be read
     */
    public static List<SegmentFileInfo> scanRecordingFiles(Path directoryPath) throws IOException {
        List<SegmentFileInfo> segmentFiles = new ArrayList<>();

        try (Stream<Path> files = Files.list(directoryPath)) {
            files.filter(path -> path.toString().endsWith(RECORDING_SEGMENT_SUFFIX))
                    .forEach(path -> {
                        File file = path.toFile();
                        String fileName = file.getName();
                        Matcher matcher = SEGMENT_FILE_PATTERN.matcher(fileName);

                        if (matcher.matches()) {
                            long recordingId = Long.parseLong(matcher.group(1));
                            long segmentBasePosition = Long.parseLong(matcher.group(2));
                            segmentFiles.add(new SegmentFileInfo(file, recordingId, segmentBasePosition));
                        } else {
                            System.err.println("Warning: File does not match expected pattern: " + fileName);
                        }
                    });
        }

        segmentFiles.sort((a, b) -> {
            int cmp = Long.compare(a.recordingId, b.recordingId);
            return cmp != 0 ? cmp : Long.compare(a.segmentBasePosition, b.segmentBasePosition);
        });

        return segmentFiles.subList(0, 10);
    }

    /**
     * Parses a single recording segment file and extracts all messages.
     *
     * @param segmentFile The segment file to parse
     * @return List of parsed messages
     * @throws IOException if file cannot be read
     */
    public static List<ParsedMessage> parseRecordingFile(SegmentFileInfo segmentFile) throws IOException {
        List<ParsedMessage> messages = new ArrayList<>();

        try (RandomAccessFile raf = new RandomAccessFile(segmentFile.file, "r");
             FileChannel channel = raf.getChannel()) {

            long fileSize = channel.size();
            ByteBuffer buffer = ByteBuffer.allocate(HEADER_LENGTH);
            UnsafeBuffer headerBuffer = new UnsafeBuffer(buffer);
            DataHeaderFlyweight headerFlyweight = new DataHeaderFlyweight(headerBuffer);

            long currentOffset = 0;
            while (currentOffset < fileSize) {
                // Read frame header
                buffer.clear();

                int totalBytesReadFromHeader = 0;

                while (totalBytesReadFromHeader != HEADER_LENGTH) {
                    int bytesRead = channel.read(buffer, currentOffset + totalBytesReadFromHeader);
                    if (bytesRead < HEADER_LENGTH) {
                        break; // End of file or incomplete header
                    }
                    totalBytesReadFromHeader += bytesRead;
                }


                buffer.flip();
                headerFlyweight.wrap(headerBuffer, 0, HEADER_LENGTH);

                // Extract header fields using Aeron utilities
                int frameLength = FrameDescriptor.frameLength(headerBuffer, 0);
                if (frameLength <= 0) {
                    break; // Invalid frame or end of data
                }

                int frameType = FrameDescriptor.frameType(headerBuffer, 0);
                byte flags = FrameDescriptor.frameFlags(headerBuffer, 0);

                // Skip padding frames, process data frames
                if (frameType == HDR_TYPE_DATA) {
                    // Extract all header fields
                    int sessionId = DataHeaderFlyweight.sessionId(headerBuffer, 0);
                    int streamId = DataHeaderFlyweight.streamId(headerBuffer, 0);
                    int termId = DataHeaderFlyweight.termId(headerBuffer, 0);
                    int termOffset = DataHeaderFlyweight.termOffset(headerBuffer, 0);
                    long reservedValue = DataHeaderFlyweight.reservedValue(headerBuffer, 0);

                    // Calculate payload information
                    int payloadLength = frameLength - HEADER_LENGTH;
                    long payloadOffset = currentOffset + HEADER_LENGTH;

                    // Read payload data if present
                    byte[] payloadData = new byte[payloadLength];
                    if (payloadLength > 0) {
                        ByteBuffer payloadBuffer = ByteBuffer.allocate(payloadLength);
                        channel.read(payloadBuffer, payloadOffset);
                        payloadBuffer.flip();
                        payloadBuffer.get(payloadData);
                    }

                    // Create parsed message
                    ParsedMessage message = new ParsedMessage(
                            segmentFile.file.getName(),
                            currentOffset,
                            payloadOffset,
                            frameLength,
                            payloadLength,
                            frameType,
                            flags,
                            sessionId,
                            streamId,
                            termId,
                            termOffset,
                            reservedValue,
                            payloadData
                    );

                    messages.add(message);
                }

                // Move to next frame (aligned to frame boundary)
                int alignedLength = BitUtil.align(frameLength, FRAME_ALIGNMENT);
                currentOffset += alignedLength;
            }
        }

        return messages;
    }

    /**
     * Parses all recording files in a directory and extracts all messages.
     *
     * @param directoryPath Path to directory containing recording files
     * @return List of all parsed messages from all files
     * @throws IOException if files cannot be read
     */
    public static List<ParsedMessage> parseAllRecordingFiles(Path directoryPath) throws IOException {
        List<SegmentFileInfo> segmentFiles = scanRecordingFiles(directoryPath);
        List<ParsedMessage> allMessages = new ArrayList<>();

        System.out.println("Found " + segmentFiles.size() + " recording segment files:");
        for (SegmentFileInfo segmentFile : segmentFiles) {
            System.out.println("  " + segmentFile);
        }
        System.out.println();

        for (SegmentFileInfo segmentFile : segmentFiles) {
            try {
                List<ParsedMessage> messages = parseRecordingFile(segmentFile);
                allMessages.addAll(messages);
                System.out.println("Parsed " + messages.size() + " messages from " + segmentFile.file.getName());
            } catch (IOException e) {
                System.err.println("Error parsing file " + segmentFile.file.getName() + ": " + e.getMessage());
            }
        }

        return allMessages;
    }

    /**
     * Example usage and demonstration.
     */
    public static void main(String[] args) throws IOException {


        Path directoryPath = Path.of("/Users/<USER>/core/archive");


        System.out.println("Scanning directory: " + directoryPath);
        System.out.println("=====================================");

        List<ParsedMessage> allMessages = parseAllRecordingFiles(directoryPath);

        System.out.println("\nTotal messages found: " + allMessages.size());
        System.out.println("=====================================");

        final DirectBuffer buffer = new UnsafeBuffer();
        final OrderDecoder orderDecoder = new OrderDecoder();
        final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
        messageHeaderDecoder.wrap(buffer, 0);


        long sequence = 0;
        // Display detailed information for each message
        for (int i = 0; i < allMessages.size(); i++) {
            ParsedMessage msg = allMessages.get(i);
//            System.out.println("\nMessage " + (i + 1) + ":");
//            System.out.println("  " + msg);
//            System.out.println("  Flags: " + msg.getFlagsDescription());
//            System.out.println("  Payload preview: " + formatPayloadPreview(msg.payloadData, 32));
            buffer.wrap(msg.payloadData);
//            System.out.println("  Sequence: " + messageHeaderDecoder.sequence());
//            System.out.println("  SchemaId: " + messageHeaderDecoder.schemaId());
//            System.out.println("  TemplateId: " + messageHeaderDecoder.templateId());
            switch (messageHeaderDecoder.templateId()) {
                case OrderDecoder.TEMPLATE_ID:
                    orderDecoder.wrapAndApplyHeader(buffer, 0, messageHeaderDecoder);
                    sequence = messageHeaderDecoder.sequence();
                    System.out.println(orderDecoder);
                    break;
            }
        }
        System.out.println("the sequence is " + sequence);
    }

    /**
     * Formats payload data for display (hex dump preview).
     */
    private static String formatPayloadPreview(byte[] data, int maxBytes) {
        if (data.length == 0) {
            return "(empty)";
        }

        StringBuilder sb = new StringBuilder();
        int limit = Math.min(data.length, maxBytes);

        for (int i = 0; i < limit; i++) {
            if (i > 0 && i % 16 == 0) {
                sb.append("\n                     ");
            } else if (i > 0 && i % 8 == 0) {
                sb.append("  ");
            } else if (i > 0) {
                sb.append(" ");
            }
            sb.append(String.format("%02X", data[i] & 0xFF));
        }

        if (data.length > maxBytes) {
            sb.append(" ... (").append(data.length - maxBytes).append(" more bytes)");
        }

        return sb.toString();
    }
}
