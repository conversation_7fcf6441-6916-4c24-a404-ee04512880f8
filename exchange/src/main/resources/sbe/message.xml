<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sbe:messageSchema xmlns:sbe="http://fixprotocol.io/2016/sbe"
                   xmlns:xi="http://www.w3.org/2001/XInclude"
                   package="org.example.sbe"
                   id="1"
                   version="1"
                   semanticVersion="1.0.0"
                   description="Basic SBE messages">
    <types>
        <composite name="messageHeader" description="Message header">
            <type name="blockLength" primitiveType="uint16"/>
            <type name="templateId" primitiveType="uint16"/>
            <type name="schemaId" primitiveType="uint16"/>
            <type name="version" primitiveType="uint16"/>
            <ref name="sequence" type="Sequence"/>
        </composite>
        <composite name="varStringEncoding">
            <type name="length" primitiveType="uint32" maxValue="1073741824"/>
            <type name="varData" primitiveType="uint8" length="0" characterEncoding="UTF-8"/>
        </composite>
        <composite name="groupSizeEncoding" description="Repeating group dimensions.">
            <type name="blockLength" primitiveType="uint16"/>
            <type name="numInGroup" primitiveType="uint16"/>
        </composite>

        <enum name="OrderType" encodingType="int32">
            <validValue name="MARKET">1</validValue>
            <validValue name="LIMIT">2</validValue>
            <validValue name="TAKE_PROFIT_LIMIT">3</validValue>
        </enum>

        <type name="Timestamp" primitiveType="int64"/>
        <type name="Sequence" primitiveType="uint64"/>
        <type name="ModelYear" primitiveType="uint16" semanticType="ModelYear"/>
        <type name="VehicleCode" primitiveType="char" length="6" semanticType="VehicleCode"/>
        <type name="someNumbers" primitiveType="int32" length="5" semanticType="SomeArray"/>
        <set name="OptionalExtras" encodingType="uint8" semanticType="Extras">
            <choice name="sunRoof">0</choice>
            <choice name="sportsPack">1</choice>
            <choice name="cruiseControl">2</choice>
        </set>
    </types>

    <sbe:message name="SampleA" id="1" description="Simple sample">
        <field name="orderType" id="1" type="OrderType"/>
        <data name="message" id="2" type="varStringEncoding"/>
    </sbe:message>

    <sbe:message name="SampleGroup" id="2" description="Sample with group">
        <field name="timestamp" id="1" type="Timestamp"/>
        <group name="group" id="10" dimensionType="groupSizeEncoding">
            <field name="groupField1" id="11" type="uint16"/>
            <field name="groupField2" id="12" type="uint16"/>
            <data name="groupField3" id="13" type="varStringEncoding"/>
        </group>
        <data name="message" id="2" type="varStringEncoding"/>
    </sbe:message>

    <sbe:message name="MultiInstrumentOrder" id="3" description="Simple sample">
        <field name="instrumentIds" id="1" type="someNumbers"/>
        <field name="orderOptions" id="2" type="OptionalExtras"/>
    </sbe:message>

    <sbe:message name="Order" id="4" description="Simple sample">
        <field name="orderType" id="1" type="OrderType"/>
        <field name="accountMsb" id="2" type="uint64"/>
        <field name="accountLsb" id="3" type="uint64"/>
        <field name="quantity" id="4" type="uint64"/>
        <field name="price" id="5" type="uint64"/>
    </sbe:message>

</sbe:messageSchema>