<#--
This is a custom SBE template for Java encoders that automatically implements MessageFlyweight
Based on the original SBE java-encoder.ftl template with MessageFlyweight interface added
-->
<#include "java-util.ftl">
<#assign className = encoderName>
<#assign implementsClause = "implements MessageFlyweight">
/*
 * Copyright 2013-2024 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package ${packageName};

import org.agrona.MutableDirectBuffer;
import org.agrona.DirectBuffer;
import org.agrona.sbe.*;

/**
 * ${description}
 * 
 * This class automatically implements MessageFlyweight for polymorphic message handling.
 */
@SuppressWarnings("all")
public final class ${className} ${implementsClause}
{
    public static final int BLOCK_LENGTH = ${blockLength};
    public static final int TEMPLATE_ID = ${templateId};
    public static final int SCHEMA_ID = ${schemaId};
    public static final int SCHEMA_VERSION = ${schemaVersion};
    public static final String SEMANTIC_VERSION = "${semanticVersion}";
    public static final java.nio.ByteOrder BYTE_ORDER = java.nio.ByteOrder.${byteOrder};

    private final ${className} parentMessage = this;
    private MutableDirectBuffer buffer;
    private int offset;
    private int limit;

    public int sbeBlockLength()
    {
        return BLOCK_LENGTH;
    }

    public int sbeTemplateId()
    {
        return TEMPLATE_ID;
    }

    public int sbeSchemaId()
    {
        return SCHEMA_ID;
    }

    public int sbeSchemaVersion()
    {
        return SCHEMA_VERSION;
    }

    public String sbeSemanticType()
    {
        return "${semanticType}";
    }

    public MutableDirectBuffer buffer()
    {
        return buffer;
    }

    public int offset()
    {
        return offset;
    }

    public ${className} wrap(final MutableDirectBuffer buffer, final int offset)
    {
        if (buffer != this.buffer)
        {
            this.buffer = buffer;
        }
        this.offset = offset;
        limit(offset + BLOCK_LENGTH);

        return this;
    }

    public ${className} wrapAndApplyHeader(
        final MutableDirectBuffer buffer,
        final int offset,
        final MessageHeaderEncoder headerEncoder)
    {
        headerEncoder
            .wrap(buffer, offset)
            .blockLength(BLOCK_LENGTH)
            .templateId(TEMPLATE_ID)
            .schemaId(SCHEMA_ID)
            .version(SCHEMA_VERSION);

        return wrap(buffer, offset + MessageHeaderEncoder.ENCODED_LENGTH);
    }

    public int encodedLength()
    {
        return limit - offset;
    }

    public int limit()
    {
        return limit;
    }

    public void limit(final int limit)
    {
        this.limit = limit;
    }

    // MessageFlyweight interface implementation
    public ${className} reset()
    {
        buffer = null;
        offset = 0;
        limit = 0;
        return this;
    }

<#list fields as field>
    <#if field.isConstant()>
    public static ${javaTypeName(field.type)} ${field.name}()
    {
        return ${field.value};
    }

    <#else>
    public static int ${field.name}Id()
    {
        return ${field.id};
    }

    public static int ${field.name}SinceVersion()
    {
        return ${field.sinceVersion};
    }

    public static int ${field.name}EncodingOffset()
    {
        return ${field.offset};
    }

    public static int ${field.name}EncodingLength()
    {
        return ${field.encodingLength};
    }

    public static String ${field.name}MetaAttribute(final MetaAttribute metaAttribute)
    {
        if (MetaAttribute.PRESENCE == metaAttribute)
        {
            return "${field.presence}";
        }
        <#if field.epoch?has_content>
        if (MetaAttribute.EPOCH == metaAttribute)
        {
            return "${field.epoch}";
        }
        </#if>
        <#if field.timeUnit?has_content>
        if (MetaAttribute.TIME_UNIT == metaAttribute)
        {
            return "${field.timeUnit}";
        }
        </#if>
        <#if field.semanticType?has_content>
        if (MetaAttribute.SEMANTIC_TYPE == metaAttribute)
        {
            return "${field.semanticType}";
        }
        </#if>
        <#if field.characterEncoding?has_content>
        if (MetaAttribute.CHARACTERENCODING == metaAttribute)
        {
            return "${field.characterEncoding}";
        }
        </#if>

        return "";
    }

        <#if field.type.isEnumType()>
    public ${className} ${field.name}(final ${javaTypeName(field.type)} value)
    {
        buffer.putInt(offset + ${field.offset}, value.value(), BYTE_ORDER);
        return this;
    }
        <#elseif field.type.isSetType()>
    public ${className} ${field.name}(final ${javaTypeName(field.type)} value)
    {
        buffer.put${field.type.primitiveType.javaTypeName?cap_first}(offset + ${field.offset}, value.value());
        return this;
    }
        <#elseif field.type.isCompositeType()>
    public ${javaTypeName(field.type)}Encoder ${field.name}()
    {
        return ${field.name}.wrap(buffer, offset + ${field.offset});
    }
        <#else>
    public ${className} ${field.name}(final ${javaTypeName(field.type)} value)
    {
        buffer.put${field.type.primitiveType.javaTypeName?cap_first}(offset + ${field.offset}, value<#if field.type.isCharArrayType()>, 0, ${field.type.length}</#if>);
        return this;
    }
        </#if>

    </#if>
</#list>

<#list groups as group>
    <#assign groupClassName = formatClassName(group.name) + "Encoder">
    public ${groupClassName} ${group.name}Count(final int count)
    {
        ${group.name}.wrap(parentMessage, buffer, count);
        return ${group.name};
    }

    public static final class ${groupClassName}
    {
        public static final int HEADER_SIZE = ${group.headerSize};
        private final GroupSizeEncodingEncoder dimensions = new GroupSizeEncodingEncoder();
        private ${className} parentMessage;
        private MutableDirectBuffer buffer;
        private int count;
        private int index;
        private int offset;
        private int initialLimit;

        public void wrap(final ${className} parentMessage, final MutableDirectBuffer buffer, final int count)
        {
            if (count < 0 || count > ${group.maxSize})
            {
                throw new IllegalArgumentException("count outside allowed range: count=" + count);
            }

            this.parentMessage = parentMessage;
            this.buffer = buffer;
            this.count = count;
            this.index = 0;
            this.offset = parentMessage.limit();
            this.initialLimit = parentMessage.limit();
            parentMessage.limit(this.offset + HEADER_SIZE);
            dimensions.wrap(buffer, this.offset);
            dimensions.blockLength((int)${group.blockLength});
            dimensions.numInGroup((int)count);
        }

        public static int sbeHeaderSize()
        {
            return HEADER_SIZE;
        }

        public static int sbeBlockLength()
        {
            return ${group.blockLength};
        }

        public ${groupClassName} next()
        {
            if (index >= count)
            {
                throw new java.util.NoSuchElementException();
            }

            offset = parentMessage.limit();
            parentMessage.limit(offset + sbeBlockLength());
            ++index;

            return this;
        }

        public int count()
        {
            return count;
        }

        public boolean hasNext()
        {
            return index < count;
        }

        public int index()
        {
            return index;
        }

        public void resetCountToIndex()
        {
            count = index;
            dimensions.numInGroup(count);
        }

    <#list group.fields as field>
        <#if field.isConstant()>
        public static ${javaTypeName(field.type)} ${field.name}()
        {
            return ${field.value};
        }

        <#else>
        public static int ${field.name}Id()
        {
            return ${field.id};
        }

        public static int ${field.name}SinceVersion()
        {
            return ${field.sinceVersion};
        }

        public static int ${field.name}EncodingOffset()
        {
            return ${field.offset};
        }

        public static int ${field.name}EncodingLength()
        {
            return ${field.encodingLength};
        }

        public static String ${field.name}MetaAttribute(final MetaAttribute metaAttribute)
        {
            if (MetaAttribute.PRESENCE == metaAttribute)
            {
                return "${field.presence}";
            }

            return "";
        }

            <#if field.type.isEnumType()>
        public ${groupClassName} ${field.name}(final ${javaTypeName(field.type)} value)
        {
            buffer.putInt(offset + ${field.offset}, value.value(), BYTE_ORDER);
            return this;
        }
            <#elseif field.type.isSetType()>
        public ${groupClassName} ${field.name}(final ${javaTypeName(field.type)} value)
        {
            buffer.put${field.type.primitiveType.javaTypeName?cap_first}(offset + ${field.offset}, value.value());
            return this;
        }
            <#elseif field.type.isCompositeType()>
        public ${javaTypeName(field.type)}Encoder ${field.name}()
        {
            return ${field.name}.wrap(buffer, offset + ${field.offset});
        }
            <#else>
        public ${groupClassName} ${field.name}(final ${javaTypeName(field.type)} value)
        {
            buffer.put${field.type.primitiveType.javaTypeName?cap_first}(offset + ${field.offset}, value<#if field.type.isCharArrayType()>, 0, ${field.type.length}</#if>);
            return this;
        }
            </#if>

        </#if>
    </#list>
    }

    private final ${groupClassName} ${group.name} = new ${groupClassName}();

</#list>

    public String toString()
    {
        if (null == buffer)
        {
            return "";
        }

        return appendTo(new StringBuilder()).toString();
    }

    public StringBuilder appendTo(final StringBuilder builder)
    {
        if (null == buffer)
        {
            return builder;
        }

        final ${decoderName} decoder = new ${decoderName}();
        decoder.wrap(buffer, offset, BLOCK_LENGTH, SCHEMA_VERSION);

        return decoder.appendTo(builder);
    }
}
