/*
 * This source file was generated by the Gradle 'init' task
 */
package org.example;

import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.*;
import org.junit.jupiter.api.Test;

import java.nio.ByteBuffer;

import static org.junit.jupiter.api.Assertions.*;

class AppTest {


    @Test
    public void testEncoderAndGroupEncoder() {

        UnsafeBuffer unsafeBuffer = new UnsafeBuffer(ByteBuffer.allocate(1024));
        ExecutionReportEncoder executionReportEncoder = new ExecutionReportEncoder();
        MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
        executionReportEncoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderEncoder);
        messageHeaderEncoder.sequence(1L);

        executionReportEncoder.executionId(1L);
        executionReportEncoder.orderId(2L);
        executionReportEncoder.executionType(ExecutionType.PARTIAL_FILL);
        executionReportEncoder.executedQuantity(100L);

        ExecutionReportEncoder.TradesEncoder tradesEncoder = executionReportEncoder.tradesCount(2);
        for (int index = 0; index < 2; index++) {
            tradesEncoder = tradesEncoder.next();
            tradesEncoder.tradeId(123L + index);
            tradesEncoder.tradePrice(100L + index);
        }


        ExecutionReportDecoder executionReportDecoder = new ExecutionReportDecoder();
        MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
        executionReportDecoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderDecoder);

        assertEquals(1L, messageHeaderDecoder.sequence());
        assertEquals(2, executionReportDecoder.trades().count());

        ExecutionReportDecoder.TradesDecoder tradesDecoder = executionReportDecoder.trades();
        
        while(tradesDecoder.hasNext()){
            tradesDecoder = tradesDecoder.next();
            System.out.println(tradesDecoder.tradeId());
            System.out.println(tradesDecoder.tradePrice());
        }


    }
}
