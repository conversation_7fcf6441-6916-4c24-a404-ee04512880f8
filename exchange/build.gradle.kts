/*
 * This file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java application project to get you started.
 * For more details on building Java & JVM projects, please refer to https://docs.gradle.org/8.14.2/userguide/building_java_projects.html in the Gradle documentation.
 */

plugins {
    // Apply the application plugin to add support for building a CLI application in Java.
    application
}

repositories {
    // Use Maven Central for resolving dependencies.
    mavenCentral()
}

dependencies {
    implementation("io.aeron:aeron-all:1.42.1")
    implementation("uk.co.real-logic:sbe-tool:1.30.0")
    implementation("org.slf4j:slf4j-api:2.0.17")
    implementation("ch.qos.logback:logback-classic:1.5.18")
    testImplementation(libs.junit.jupiter)
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

}

// Define SBE generation task
tasks.register<JavaExec>("generateSbe") {
    group = "build"
    description = "Generate SBE stubs"
    mainClass.set("uk.co.real_logic.sbe.SbeTool")
    classpath = configurations.getByName("runtimeClasspath")
    val generatedDir = "$buildDir/generated-sbe/sbe"
    systemProperties(
        mapOf(
            "sbe.output.dir" to generatedDir,
            "sbe.target.language" to "Java",
            "sbe.validation.stop.on.error" to "true",
            "sbe.validation.xsd" to "src/main/resources/sbe/sbe.xsd"
        )
    )
    args =
        listOf("src/main/resources/sbe/message.xml")
}

tasks.compileJava {
    dependsOn("generateSbe")
}

// Add the generated sources to the main source set
sourceSets {
    main {
        java {
            srcDir("$buildDir/generated-sbe/sbe")
        }
    }
}

// Apply a specific Java toolchain to ease working on different environments.
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

application {
    // Define the main class for the application.
    mainClass.set("org.example.App")
}

tasks.test {
    useJUnitPlatform()
}