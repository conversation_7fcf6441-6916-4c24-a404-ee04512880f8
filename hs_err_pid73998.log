#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x0000000102993ebc, pid=73998, tid=41731
#
# JRE version: OpenJDK Runtime Environment Zulu21.40+17-CA (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Zulu21.40+17-CA (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# V  [libjvm.dylib+0xa3bebc]  Unsafe_GetLongVolatile(JNIEnv_*, _jobject*, _jobject*, long)+0x130
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://www.azul.com/support/
#

---------------  S U M M A R Y ------------

Command Line: -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true --add-opens=java.base/sun.nio.ch=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=n,suspend=y,address=127.0.0.1:64583 -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2025.1/captureAgent/debugger-agent.jar=file:///var/folders/1q/nff07fl14ss1md1tvlg_hj540000gn/T/capture12551269003144505367.props -Dfile.encoding=UTF-8 -Duser.country=TW -Duser.language=en -Duser.variant org.example.App

Host: "Mac14,2" arm64, 8 cores, 16G, Darwin 24.5.0, macOS 15.5 (24F74)
Time: Sun Jun 15 21:07:42 2025 CST elapsed time: 47.480551 seconds (0d 0h 0m 47s)

---------------  T H R E A D  ---------------

Current thread (0x000000011f903000):  JavaThread ""                 [_thread_in_vm, id=41731, stack(0x0000000172408000,0x000000017260b000) (2060K)]

Stack: [0x0000000172408000,0x000000017260b000],  sp=0x00000001726099f0,  free space=2054k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [libjvm.dylib+0xa3bebc]  Unsafe_GetLongVolatile(JNIEnv_*, _jobject*, _jobject*, long)+0x130
j  jdk.internal.misc.Unsafe.getLongVolatile(Ljava/lang/Object;J)J+0 java.base@21.0.6
j  sun.misc.Unsafe.getLongVolatile(Ljava/lang/Object;J)J+5 jdk.unsupported@21.0.6
j  org.agrona.concurrent.status.UnsafeBufferPosition.getVolatile()J+11
j  org.agrona.concurrent.status.UnsafeBufferPosition.toString()Ljava/lang/String;+50
j  com.intellij.rt.debugger.BatchEvaluatorServer.evaluate([Ljava/lang/Object;)Ljava/lang/String;+44
j  com.intellij.rt.debugger.BatchEvaluatorServer.evaluate4(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;+20
v  ~StubRoutines::call_stub 0x0000000112d70154
V  [libjvm.dylib+0x4ba9a4]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x3e0
V  [libjvm.dylib+0x530bb8]  jni_invoke_static(JNIEnv_*, JavaValue*, _jobject*, JNICallType, _jmethodID*, JNI_ArgumentPusher*, JavaThread*)+0x170
V  [libjvm.dylib+0x533be0]  jni_CallStaticObjectMethodA+0x110
C  [libjdwp.dylib+0x1b15c]  invoker_doInvoke+0x3f8
C  [libjdwp.dylib+0x174d0]  event_callback+0x55c
C  [libjdwp.dylib+0x137b8]  cbSingleStep+0x158
V  [libjvm.dylib+0x685ce0]  JvmtiExport::post_single_step(JavaThread*, Method*, unsigned char*)+0x328
V  [libjvm.dylib+0x68598c]  JvmtiExport::at_single_stepping_point(JavaThread*, Method*, unsigned char*)+0x1b0
V  [libjvm.dylib+0x4b271c]  InterpreterRuntime::at_safepoint(JavaThread*)+0xac
j  io.aeron.ConcurrentPublication.offer(Lorg/agrona/DirectBuffer;IILio/aeron/ReservedValueSupplier;)J+193
j  io.aeron.Publication.offer(Lorg/agrona/DirectBuffer;II)J+5
j  org.example.services.PositionNodeApp.publishOrderEvent()I+93
j  org.example.services.PositionNodeApp.doWork()I+38
J 1129% c2 org.agrona.concurrent.AgentRunner.workLoop(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V (17 bytes) @ 0x000000011336549c [0x0000000113365340+0x000000000000015c]
j  org.agrona.concurrent.AgentRunner.run()V+63
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.6
j  java.lang.Thread.run()V+19 java.base@21.0.6
v  ~StubRoutines::call_stub 0x0000000112d70154
V  [libjvm.dylib+0x4ba9a4]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x3e0
V  [libjvm.dylib+0x4b98d4]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0x140
V  [libjvm.dylib+0x4b99a0]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, JavaThread*)+0x64
V  [libjvm.dylib+0x58b66c]  thread_entry(JavaThread*, JavaThread*)+0x9c
V  [libjvm.dylib+0x4cf0d4]  JavaThread::thread_main_inner()+0x98
V  [libjvm.dylib+0xa09d20]  Thread::call_run()+0xc8
V  [libjvm.dylib+0x82c57c]  thread_native_entry(Thread*)+0x118
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  jdk.internal.misc.Unsafe.getLongVolatile(Ljava/lang/Object;J)J+0 java.base@21.0.6
j  sun.misc.Unsafe.getLongVolatile(Ljava/lang/Object;J)J+5 jdk.unsupported@21.0.6
j  org.agrona.concurrent.status.UnsafeBufferPosition.getVolatile()J+11
j  org.agrona.concurrent.status.UnsafeBufferPosition.toString()Ljava/lang/String;+50
j  com.intellij.rt.debugger.BatchEvaluatorServer.evaluate([Ljava/lang/Object;)Ljava/lang/String;+44
j  com.intellij.rt.debugger.BatchEvaluatorServer.evaluate4(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;+20
v  ~StubRoutines::call_stub 0x0000000112d70154
j  io.aeron.ConcurrentPublication.offer(Lorg/agrona/DirectBuffer;IILio/aeron/ReservedValueSupplier;)J+193
j  io.aeron.Publication.offer(Lorg/agrona/DirectBuffer;II)J+5
j  org.example.services.PositionNodeApp.publishOrderEvent()I+93
j  org.example.services.PositionNodeApp.doWork()I+38
J 1129% c2 org.agrona.concurrent.AgentRunner.workLoop(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V (17 bytes) @ 0x000000011336549c [0x0000000113365340+0x000000000000015c]
j  org.agrona.concurrent.AgentRunner.run()V+63
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.6
j  java.lang.Thread.run()V+19 java.base@21.0.6
v  ~StubRoutines::call_stub 0x0000000112d70154

siginfo: si_signo: 11 (SIGSEGV), si_code: 2 (SEGV_ACCERR), si_addr: 0x0000000146e0e100

Registers:
 x0=0x0000000000000000  x1=0x0000000fffffc110  x2=0x0000000000000000  x3=0x0000000146e0e100
 x4=0x0000000000000000  x5=0x00000001726099b0  x6=0x0000000084400001  x7=0x000000011f903000
 x8=0x0000000146e0e100  x9=0x0000000000000006 x10=0x0000000000000006 x11=0x0000000000000007
x12=0x00000078004904b0 x13=0x0000000000000280 x14=0x0000000089c36813 x15=0x0000000089a36014
x16=0x000000018d9539d4 x17=0x0000000000000101 x18=0x0000000000000000 x19=0x000000011f903000
x20=0x0000000000000001 x21=0x0000000146e0e100 x22=0x0000000000000000 x23=0x0000000112d79740
x24=0x0000000172609ad8 x25=0x0000000172609f68 x26=0x0000007800491f38 x27=0x0000000000000000
x28=0x000000011f903000  fp=0x0000000172609a20  lr=0x0000000102993de4  sp=0x00000001726099f0
pc=0x0000000102993ebc cpsr=0x0000000040001000

Register to memory mapping:

x0 =0x0 is null
x1 =0x0000000fffffc110 points into unknown readable memory: 0x2010002030300000 | 00 00 30 30 20 00 10 20
x2 =0x0 is null
x3 =0x0000000146e0e100 is an unknown value
x4 =0x0 is null
x5 =0x00000001726099b0 is pointing into the stack for thread: 0x000000011f903000
x6 =0x0000000084400001 is an unknown value
x7 =0x000000011f903000 is a thread
x8 =0x0000000146e0e100 is an unknown value
x9 =0x0000000000000006 is an unknown value
x10=0x0000000000000006 is an unknown value
x11=0x0000000000000007 is an unknown value
x12={method} {0x00000078004904b0} 'getLongVolatile' '(Ljava/lang/Object;J)J' in 'jdk/internal/misc/Unsafe'
x13=0x0000000000000280 is an unknown value
x14=0x0000000089c36813 is an unknown value
x15=0x0000000089a36014 is an unknown value
x16=0x000000018d9539d4: pthread_jit_write_protect_np+0 in /usr/lib/system/libsystem_pthread.dylib at 0x000000018d94c000
x17=0x0000000000000101 is an unknown value
x18=0x0 is null
x19=0x000000011f903000 is a thread
x20=0x0000000000000001 is an unknown value
x21=0x0000000146e0e100 is an unknown value
x22=0x0 is null
x23=0x0000000112d79740 is at code_begin+0 in an Interpreter codelet
method entry point (kind = zerolocals)  [0x0000000112d79740, 0x0000000112d79bf0]  1200 bytes
x24=0x0000000172609ad8 is pointing into the stack for thread: 0x000000011f903000
x25=0x0000000172609f68 is pointing into the stack for thread: 0x000000011f903000
x26=0x0000007800491f38 is pointing into metadata
x27=0x0 is null
x28=0x000000011f903000 is a thread
 fp=0x0000000172609a20 is pointing into the stack for thread: 0x000000011f903000
 lr=0x0000000102993de4: _ZL22Unsafe_GetLongVolatileP7JNIEnv_P8_jobjectS2_l+0x58 in /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/server/libjvm.dylib at 0x0000000101f58000
 sp=0x00000001726099f0 is pointing into the stack for thread: 0x000000011f903000

Top of Stack: (sp=0x00000001726099f0)
0x00000001726099f0:   0000000172609a50 0000000000000001
0x0000000172609a00:   0000000000000000 0000000102cb6258
0x0000000172609a10:   0000000172609a30 0000000112d77eac
0x0000000172609a20:   0000000172609aa0 0000000112d7ad40
0x0000000172609a30:   0000000112d7aa54 00000078004904b0
0x0000000172609a40:   0000000000000000 0000000172609b30
0x0000000172609a50:   0000000172609a50 0000000000000000
0x0000000172609a60:   0000000000000007 0000007800491f38
0x0000000172609a70:   00000007ffe82870 0000000172609a40
0x0000000172609a80:   0000000000000000 00000078004904b0
0x0000000172609a90:   0000000000000000 0000000172609aa0
0x0000000172609aa0:   0000000172609b30 0000000112d75690
0x0000000172609ab0:   0000000000000000 0000000000000000
0x0000000172609ac0:   0000000146e0e100 0000000000000000
0x0000000172609ad0:   0000000000000000 000000070e818078
0x0000000172609ae0:   0000000172609ae0 00000001412590a5
0x0000000172609af0:   0000000000000005 000000014125ad88
0x0000000172609b00:   000000070e8ef958 0000000172609aa0
0x0000000172609b10:   00000001415f1920 00000001412590e0
0x0000000172609b20:   0000000172609ac0 0000000172609b20
0x0000000172609b30:   0000000172609bb0 0000000112d75690
0x0000000172609b40:   0000000146e0e100 0000000000000000
0x0000000172609b50:   0000000000000000 000000070e8383e8
0x0000000172609b60:   0000000172609b60 0000000141550c9b
0x0000000172609b70:   0000000000000002 00000001417af5f8
0x0000000172609b80:   000000070e9099a0 0000000172609b20
0x0000000172609b90:   000000014180e558 0000000141550cb8
0x0000000172609ba0:   0000000172609b40 0000000172609ba0
0x0000000172609bb0:   0000000172609c20 0000000112d75690
0x0000000172609bc0:   000000070e8e1dc0 000000070f029040
0x0000000172609bd0:   0000000172609bd0 0000000141551342
0x0000000172609be0:   0000000000000003 00000001417af5f8 

Instructions: (pc=0x0000000102993ebc)
0x0000000102993dbc:   1f 05 00 71 68 00 00 54 e0 03 13 aa 2a 4b ea 97
0x0000000102993dcc:   f3 00 00 b4 74 6a 43 b9 d4 00 00 34 7f 6a 03 b9
0x0000000102993ddc:   00 00 80 52 55 d1 f7 97 02 00 00 14 14 00 80 52
0x0000000102993dec:   88 18 00 d0 08 21 0e 91 08 01 40 39 69 32 11 91
0x0000000102993dfc:   ca 00 80 52 2a fd 9f 88 e8 02 00 34 68 42 11 91
0x0000000102993e0c:   08 c1 bf f8 08 03 00 37 68 4a 44 b9 1f 05 1e 72
0x0000000102993e1c:   60 00 00 54 e0 03 13 aa 47 4f ea 97 68 32 11 91
0x0000000102993e2c:   c9 00 80 52 09 fd 9f 88 f6 02 00 b4 c8 06 40 92
0x0000000102993e3c:   1f 09 00 f1 c0 02 00 54 1f 05 00 f1 21 03 00 54
0x0000000102993e4c:   c0 06 00 d1 c8 16 00 f0 08 7d 42 f9 00 01 3f d6
0x0000000102993e5c:   15 00 00 14 bf 3b 03 d5 68 42 11 91 08 c1 bf f8
0x0000000102993e6c:   48 fd 07 36 e0 03 13 aa 21 00 80 52 02 00 80 52
0x0000000102993e7c:   3b 05 fa 97 68 4a 44 b9 1f 05 1e 72 c1 fc ff 54
0x0000000102993e8c:   e7 ff ff 17 00 00 80 d2 07 00 00 14 c0 0a 00 d1
0x0000000102993e9c:   a8 16 00 d0 08 45 40 f9 00 01 3f d6 02 00 00 14
0x0000000102993eac:   c0 02 40 f9 28 00 80 52 68 e2 11 39 08 00 15 8b
0x0000000102993ebc:   08 c1 bf f8 e8 07 00 f9 f5 07 40 f9 7f e2 11 39
0x0000000102993ecc:   76 ce 40 f9 c8 0a 40 f9 09 01 40 f9 89 00 00 b4
0x0000000102993edc:   e0 03 16 aa 75 7f e8 97 c8 0a 40 f9 c9 06 40 f9
0x0000000102993eec:   28 09 00 f9 c8 0e 40 f9 c9 06 40 f9 28 0d 00 f9
0x0000000102993efc:   c8 12 40 f9 c9 06 40 f9 28 11 00 f9 60 82 0e 91
0x0000000102993f0c:   b4 63 e5 97 bf 3b 03 d5 68 32 11 91 89 00 80 52
0x0000000102993f1c:   09 fd 9f 88 f3 00 00 b4 68 6a 43 b9 1f 01 14 6b
0x0000000102993f2c:   80 00 00 54 74 6a 03 b9 e0 03 14 aa ff d0 f7 97
0x0000000102993f3c:   e0 03 15 aa fd 7b 43 a9 f4 4f 42 a9 f6 57 41 a9
0x0000000102993f4c:   ff 03 01 91 c0 03 5f d6 f8 5f bc a9 f6 57 01 a9
0x0000000102993f5c:   f4 4f 02 a9 fd 7b 03 a9 fd c3 00 91 f5 03 04 aa
0x0000000102993f6c:   f6 03 03 aa f7 03 02 aa 13 00 0f d1 08 c0 02 91
0x0000000102993f7c:   08 c1 bf b8 89 d5 9b 12 08 01 09 0b 1f 05 00 71
0x0000000102993f8c:   68 00 00 54 e0 03 13 aa b7 4a ea 97 f3 00 00 b4
0x0000000102993f9c:   74 6a 43 b9 d4 00 00 34 7f 6a 03 b9 00 00 80 52
0x0000000102993fac:   e2 d0 f7 97 02 00 00 14 14 00 80 52 88 18 00 d0 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0000000172609a50 is pointing into the stack for thread: 0x000000011f903000
stack at sp + 1 slots: 0x0000000000000001 is an unknown value
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: 0x0000000102cb6258: _ZN19TemplateInterpreter13_active_tableE+0 in /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/server/libjvm.dylib at 0x0000000101f58000
stack at sp + 4 slots: 0x0000000172609a30 is pointing into the stack for thread: 0x000000011f903000
stack at sp + 5 slots: 0x0000000112d77eac is at code_begin+44 in an Interpreter codelet
result handlers for native calls  [0x0000000112d77e80, 0x0000000112d77ec8]  72 bytes
stack at sp + 6 slots: 0x0000000172609aa0 is pointing into the stack for thread: 0x000000011f903000
stack at sp + 7 slots: 0x0000000112d7ad40 is at code_begin+1280 in an Interpreter codelet
native method entry point (kind = native)  [0x0000000112d7a840, 0x0000000112d7b380]  2880 bytes


Compiled method (c2) 47485 1129 %     4       org.agrona.concurrent.AgentRunner::workLoop @ 0 (17 bytes)
 total in heap  [0x0000000113365110,0x0000000113365d18] = 3080
 relocation     [0x0000000113365260,0x0000000113365320] = 192
 main code      [0x0000000113365340,0x0000000113365828] = 1256
 stub code      [0x0000000113365828,0x00000001133658f8] = 208
 oops           [0x00000001133658f8,0x0000000113365908] = 16
 metadata       [0x0000000113365908,0x0000000113365998] = 144
 scopes data    [0x0000000113365998,0x0000000113365b40] = 424
 scopes pcs     [0x0000000113365b40,0x0000000113365c50] = 272
 dependencies   [0x0000000113365c50,0x0000000113365c70] = 32
 handler table  [0x0000000113365c70,0x0000000113365d00] = 144
 nul chk table  [0x0000000113365d00,0x0000000113365d18] = 24

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x0000000141407ac0} 'workLoop' '(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V' in 'org/agrona/concurrent/AgentRunner'
  0x0000000113365340: 0000 20d4 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | e953 40d1 | 3f01 00f9 | ffc3 01d1 
  0x0000000113365360: fd7b 06a9 | 0826 0018 | 8923 40b9 | 1f01 09eb | 0125 0054 | 8ac7 41f9 | 33f4 40a9 | 3400 40f9 
  0x0000000113365380: 5501 40f9 | e003 01aa | c900 0010 

  0x000000011336538c: ;   {runtime_call SharedRuntime::OSR_migration_end(long*)}
  0x000000011336538c: 08a0 85d2 | 4850 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x00000001133653a0: ;   {other}
  0x00000001133653a0: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 

  0x00000001133653b0: ; implicit exception: dispatches to 0x00000001133657e4
  0x00000001133653b0: aa0b 40b9 

  0x00000001133653b4: ;   {metadata('org/agrona/concurrent/AgentRunner')}
  0x00000001133653b4: 0c20 a0d2 | 0c40 86f2 | 5f01 0c6b | 011e 0054 | ab0a 40b9 | 6a0d 5dd2 | ad33 0091 | ae33 0091 
  0x00000001133653d4: 4a21 40f9 | acee 0091 | 2500 0014 | 0a08 40b9 

  0x00000001133653e4: ;   {metadata('java/lang/InterruptedException')}
  0x00000001133653e4: 8c20 a0d2 | 0c13 95f2 | 5f01 0c6b | 410d 0054 | fd03 00aa | 0a00 0014 | 0b08 40b9 | 6a0d 5dd2 
  0x0000000113365404: 4b25 40f9 

  0x0000000113365408: ;   {metadata('java/lang/InterruptedException')}
  0x0000000113365408: 0c13 95d2 | 8c20 a0f2 | 0c0f c0f2 | 7f01 0ceb | c10c 0054 | fd03 00aa | ea13 40f9 | 5ffd 9f08 
  0x0000000113365428: e107 40f9 

  0x000000011336542c: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [56]=Derived_oop_[8] [24]=Oop [32]=Derived_oop_[24] [16]=Derived_oop_[24] [40]=Oop }
                      ;*invokevirtual interrupt {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@44 (line 314)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x000000011336542c: 551b e997 

  0x0000000113365430: ;   {other}
  0x0000000113365430: 1f20 03d5 | 1f64 80f2 | 1f00 80f2 | f357 40a9 | fd0f 40f9 | ec1f 40f9 | 0500 0014 | 1f20 03d5 
  0x0000000113365450: ec1f 40f9 | f507 40f9 | fd0f 40f9 

  0x000000011336545c: ; ImmutableOopMap {r19=Oop rdispatch=Oop r12=Derived_oop_rdispatch rfp=Oop [32]=Derived_oop_rfp [16]=Derived_oop_rfp [40]=Oop }
                      ;*goto {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::workLoop@13 (line 296)
  0x000000011336545c: 8a2f 42f9 

  0x0000000113365460: ;   {poll}
  0x0000000113365460: 5f01 40b9 | ed0b 40f9 | ee53 42a9 | ea1b 40f9 | abfd df08 | eb0a 0034 | f357 00a9 | ed77 01a9 
  0x0000000113365480: ee53 02a9 | ea33 03a9 | e103 14aa | 09b4 94d2 | 6955 a0f2 | 0900 ccf2 

  0x0000000113365498: ; ImmutableOopMap {rfp=Oop [32]=Derived_oop_rfp [16]=Derived_oop_rfp [0]=Oop [8]=Oop [56]=Derived_oop_[8] [24]=Oop [40]=Oop }
                      ;*invokeinterface doWork {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@1 (line 304)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {virtual_call}
  0x0000000113365498: 4ccf ec97 

  0x000000011336549c: ;   {other}
  0x000000011336549c: 1f20 03d5 | 9f71 80f2 | 1f40 80f2 | e043 00b9 | f303 40f9 

  0x00000001133654b0: ; implicit exception: dispatches to 0x00000001133657b8
  0x00000001133654b0: 6a0a 40b9 

  0x00000001133654b4: ;   {metadata('org/agrona/concurrent/SleepingIdleStrategy')}
  0x00000001133654b4: 0c20 a0d2 | 0c55 87f2 | 5f01 0c6b | 4105 0054 | eb03 002a | 1f00 0071 | 0c03 0054 | ec03 13aa 
  0x00000001133654d4: 8a09 40f9 | 5f01 00f1 | ed0c 0054 

  0x00000001133654e0: ;   {metadata('java/lang/BaseVirtualThread')}
  0x00000001133654e0: 0ef6 99d2 | 4e00 a0f2 | 0e0f c0f2 | ed1b 40f9 | bf01 0eeb | c007 0054 | ec2b 04a9 | fd03 002a 
  0x0000000113365500: ;   {oop(a 'jdk/internal/misc/Unsafe'{0x000000070e818078})}
  0x0000000113365500: 010f 90d2 | 21d0 a1f2 | e100 c0f2 | e203 1f2a | e303 0aaa 

  0x0000000113365514: ; ImmutableOopMap {[0]=Oop [8]=Oop [56]=Derived_oop_[8] [24]=Oop [32]=Derived_oop_[24] [16]=Derived_oop_[24] [40]=Oop [64]=Oop }
                      ;*invokevirtual park {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.LockSupport::parkNanos@27 (line 410)
                      ; - org.agrona.concurrent.SleepingIdleStrategy::idle@9 (line 67)
                      ; - org.agrona.concurrent.AgentRunner::doWork@9 (line 305)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x0000000113365514: cf7d ff97 

  0x0000000113365518: ;   {other}
  0x0000000113365518: 1f20 03d5 | 1f81 80f2 | 1f60 80f2 | f303 40f9 | eb03 1d2a | 7f01 0071 | 0cf9 ff54 | ec1f 40f9 
  0x0000000113365538: 8afd df08 | caf8 ff34 | ec17 40f9 | fd0f 40f9 | 4117 8012 | ec07 00f9 | f30f 00f9 | eb2b 0529 
  0x0000000113365558: ; ImmutableOopMap {rfp=Oop [8]=Oop [24]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::doWork@24 (line 306)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000113365558: 8a36 e997 

  0x000000011336555c: ;   {other}
  0x000000011336555c: 1f20 03d5 | 9f89 80f2 | 1f80 80f2 | e103 13aa | e203 002a | 0972 93d2 | 0921 a0f2 | 090f c0f2 
  0x000000011336557c: ; ImmutableOopMap {rfp=Oop [32]=Derived_oop_rfp [16]=Derived_oop_rfp [0]=Oop [8]=Oop [56]=Derived_oop_[8] [24]=Oop [40]=Oop }
                      ;*invokeinterface idle {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@9 (line 305)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {virtual_call}
  0x000000011336557c: a169 0094 

  0x0000000113365580: ;   {other}
  0x0000000113365580: 1f20 03d5 | 1f8e 80f2 | 1fa0 80f2 | f303 40f9 | eb43 40b9 | e6ff ff17 

  0x0000000113365598: ;   {metadata('java/nio/channels/ClosedByInterruptException')}
  0x0000000113365598: cb20 a0d2 | 0bf6 8bf2 | 5f01 0b6b | 6103 0054 | fd03 00aa | 9dff ff17 | 4b31 40f9 

  0x00000001133655b4: ;   {metadata('java/nio/channels/ClosedByInterruptException')}
  0x00000001133655b4: 0cf6 8bd2 | cc20 a0f2 | 0c0f c0f2 | 7f01 0ceb | 2103 0054 | fd03 00aa | 95ff ff17 | 4117 8012 
  0x00000001133655d4: f353 00a9 | eb13 00b9 

  0x00000001133655dc: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::workLoop@4 (line 294)
                      ;   {runtime_call UncommonTrapBlob}
  0x00000001133655dc: 6936 e997 

  0x00000001133655e0: ;   {other}
  0x00000001133655e0: 1f20 03d5 | 1f9a 80f2 | 1fc0 80f2 | ed17 40f9 | 4117 8012 | ed07 00f9 | e02f 00b9 | ec2b 03a9 
  0x0000000113365600: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [48]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.LockSupport::parkNanos@12 (line 407)
                      ; - org.agrona.concurrent.SleepingIdleStrategy::idle@9 (line 67)
                      ; - org.agrona.concurrent.AgentRunner::doWork@9 (line 305)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000113365600: 6036 e997 

  0x0000000113365604: ;   {other}
  0x0000000113365604: 1f20 03d5 | 9f9e 80f2 | 1fe0 80f2 

  0x0000000113365610: ;   {metadata('org/agrona/concurrent/AgentTerminationException')}
  0x0000000113365610: 0b20 a0d2 | 0b8b 86f2 | 5f01 0b6b | 4104 0054 | fd03 00aa | 0800 0014 | 4a29 40f9 

  0x000000011336562c: ;   {metadata('org/agrona/concurrent/AgentTerminationException')}
  0x000000011336562c: 0b8b 86d2 | 0b20 a0f2 | 0b0f c0f2 | 5f01 0beb | 8103 0054 | fd03 00aa | ea0f 40f9 | 4a31 0091 
  0x000000011336564c: 5ffd 9f08 | e10f 40f9 | e203 1daa 

  0x0000000113365658: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [56]=Derived_oop_[8] [24]=Oop [32]=Derived_oop_[24] [16]=Derived_oop_[24] [40]=Oop }
                      ;*invokespecial handleError {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@58 (line 319)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x0000000113365658: ca1a e997 

  0x000000011336565c: ;   {other}
  0x000000011336565c: 1f20 03d5 | 9fa9 80f2 | 1f00 81f2 | f357 40a9 | fd0f 40f9 | ec1f 40f9 | 7aff ff17 | 5f01 00f1 
  0x000000011336567c: fd07 9f1a | bda7 9d5a | 4117 8012 | f307 00f9 | e037 00b9 | ecab 03a9 

  0x0000000113365694: ; ImmutableOopMap {[8]=Oop [24]=Oop [40]=Oop [56]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.LockSupport::parkNanos@3 (line 406)
                      ; - org.agrona.concurrent.SleepingIdleStrategy::idle@9 (line 67)
                      ; - org.agrona.concurrent.AgentRunner::doWork@9 (line 305)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000113365694: 3b36 e997 

  0x0000000113365698: ;   {other}
  0x0000000113365698: 1f20 03d5 | 1fb1 80f2 | 1f20 81f2 | e023 00f9 | 0200 0014 | e023 00f9 | eb07 40f9 | 6aed 0091 
  0x00000001133656b8: 4afd df08 | 4a05 0034 | ec0f 40f9 | 8a31 0091 | 5ffd 9f08 | ea23 40f9 | 4b09 40b9 | 6a0d 5dd2 
  0x00000001133656d8: 4a21 40f9 | 9d31 0091 | ec0f 00f9 | ea27 00f9 | e103 0caa | e223 40f9 

  0x00000001133656f0: ; ImmutableOopMap {[0]=Oop [8]=Oop [56]=Derived_oop_[8] [24]=Oop [32]=Derived_oop_[24] [16]=Derived_oop_[24] rfp=Derived_oop_[24] [40]=Oop [64]=Oop }
                      ;*invokespecial handleError {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@81 (line 328)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x00000001133656f0: a41a e997 

  0x00000001133656f4: ;   {other}
  0x00000001133656f4: 1f20 03d5 | 9fbc 80f2 | 1f40 81f2 | aaff df08 | 4a03 0034 | f507 40f9 | aaee 0091 | 4bfd df08 
  0x0000000113365714: 2b03 0034 | fd0f 40f9 | aa33 0091 | 5ffd 9f08 

  0x0000000113365724: ;   {metadata('java/lang/Error')}
  0x0000000113365724: 0a3c 83d2 | 2a00 a0f2 | 0a0f c0f2 | eb27 40f9 | 7f01 0aeb | 0101 0054 | ea23 40f9 | 8bc7 45b9 
  0x0000000113365744: ab02 0035 | e103 0aaa | fd7b 46a9 | ffc3 0191 

  0x0000000113365754: ;   {runtime_call _rethrow_Java}
  0x0000000113365754: 6bb8 ec17 | f303 40f9 | ec1f 40f9 | 3fff ff17 | ec0f 40f9 | d9ff ff17 | f507 40f9 | fd0f 40f9 
  0x0000000113365774: ecff ff17 | fd0f 40f9 | eaff ff17 | 410e 8012 | f353 00a9 

  0x0000000113365788: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::workLoop@0 (line 294)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000113365788: fe35 e997 

  0x000000011336578c: ;   {other}
  0x000000011336578c: 1f20 03d5 | 9fcf 80f2 | 1f60 81f2 | ea1b 00f9 | ea17 40f9 | 010d 8012 | ea07 00f9 

  0x00000001133657a8: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [48]=Oop [64]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::doWork@116 (line 336)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x00000001133657a8: f635 e997 

  0x00000001133657ac: ;   {other}
  0x00000001133657ac: 1f20 03d5 | 9fd3 80f2 | 1f80 81f2 | ea17 40f9 | 2101 8012 | ea07 00f9 | e027 00b9 

  0x00000001133657c8: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*invokeinterface idle {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@9 (line 305)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x00000001133657c8: ee35 e997 

  0x00000001133657cc: ;   {other}
  0x00000001133657cc: 1f20 03d5 | 9fd7 80f2 | 1fa0 81f2 | 0a08 40b9 | 4a0d 5dd2 | 09ff ff17 | 2111 8012 | fd03 13aa 
  0x00000001133657ec: f403 00f9 

  0x00000001133657f0: ; ImmutableOopMap {rfp=Oop [0]=Oop }
                      ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::workLoop@0 (line 294)
                      ;   {runtime_call UncommonTrapBlob}
  0x00000001133657f0: e435 e997 

  0x00000001133657f4: ;   {other}
  0x00000001133657f4: 1f20 03d5 | 9fdc 80f2 | 1fc0 81f2 | 0200 0014 | 0100 0014 | ea03 00aa | cfff ff17 | 0830 8bd2 
  0x0000000113365814: 685b a2f2 | 2800 c0f2 | 0001 3fd6 | d5fe ff17 

  0x0000000113365824: ;   {other}
  0x0000000113365824: 0100 0000 
[Stub Code]
  0x0000000113365828: ;   {no_reloc}
  0x0000000113365828: 4800 0058 | 0001 1fd6 | 80c1 da12 | 0100 0000 

  0x0000000113365838: ;   {static_stub}
  0x0000000113365838: df3f 03d5 

  0x000000011336583c: ;   {metadata(nullptr)}
  0x000000011336583c: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x0000000113365858: ;   {trampoline_stub}
  0x0000000113365858: 4800 0058 | 0001 1fd6 | c891 e912 | 0100 0000 

  0x0000000113365868: ;   {trampoline_stub}
  0x0000000113365868: 4800 0058 | 0001 1fd6 | 504c 3413 | 0100 0000 

  0x0000000113365878: ;   {trampoline_stub}
  0x0000000113365878: 4800 0058 | 0001 1fd6 | 00fc 3713 | 0100 0000 

  0x0000000113365888: ;   {trampoline_stub}
  0x0000000113365888: 4800 0058 | 0001 1fd6 | 80c1 da12 | 0100 0000 

  0x0000000113365898: ;   {trampoline_stub}
  0x0000000113365898: 4800 0058 | 0001 1fd6 | 80c1 da12 | 0100 0000 

  0x00000001133658a8: ;   {static_stub}
  0x00000001133658a8: df3f 03d5 

  0x00000001133658ac: ;   {metadata(nullptr)}
  0x00000001133658ac: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x00000001133658c8: ;   {static_stub}
  0x00000001133658c8: df3f 03d5 

  0x00000001133658cc: ;   {metadata(nullptr)}
  0x00000001133658cc: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 
[Exception Handler]
  0x00000001133658e8: ;   {runtime_call ExceptionBlob}
  0x00000001133658e8: 6699 ec17 
[Deopt Handler Code]
  0x00000001133658ec: 1e00 0010 

  0x00000001133658f0: ;   {runtime_call DeoptimizationBlob}
  0x00000001133658f0: d434 e917 | 0000 0000 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000600002aa8b20, length=21, elements={
0x000000013a013400, 0x000000011f832800, 0x000000011f833000, 0x000000011f833800,
0x000000011f834000, 0x000000011f834800, 0x000000011f83d000, 0x000000011f831000,
0x000000011f831e00, 0x000000011f852a00, 0x000000011f853200, 0x000000011f853a00,
0x000000011f861e00, 0x00000001400aa000, 0x000000011f847600, 0x000000011f903000,
0x000000011f808200, 0x000000011f964e00, 0x000000011f96ee00, 0x000000013d810e00,
0x000000011f9eac00
}

Java Threads: ( => current thread )
  0x000000013a013400 JavaThread "Reference Handler"          daemon [_thread_blocked, id=29955, stack(0x0000000170554000,0x0000000170757000) (2060K)]
  0x000000011f832800 JavaThread "Finalizer"                  daemon [_thread_blocked, id=24835, stack(0x0000000170760000,0x0000000170963000) (2060K)]
  0x000000011f833000 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=29699, stack(0x000000017096c000,0x0000000170b6f000) (2060K)]
  0x000000011f833800 JavaThread "Service Thread"             daemon [_thread_blocked, id=25603, stack(0x0000000170b78000,0x0000000170d7b000) (2060K)]
  0x000000011f834000 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=26115, stack(0x0000000170d84000,0x0000000170f87000) (2060K)]
  0x000000011f834800 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=26371, stack(0x0000000170f90000,0x0000000171193000) (2060K)]
  0x000000011f83d000 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=26627, stack(0x000000017119c000,0x000000017139f000) (2060K)]
  0x000000011f831000 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=28931, stack(0x00000001713a8000,0x00000001715ab000) (2060K)]
  0x000000011f831e00 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=27395, stack(0x00000001715b4000,0x00000001717b7000) (2060K)]
  0x000000011f852a00 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=28419, stack(0x00000001717c0000,0x00000001719c3000) (2060K)]
  0x000000011f853200 JavaThread "JDWP Command Reader"        daemon [_thread_in_native, id=28163, stack(0x00000001719cc000,0x0000000171bcf000) (2060K)]
  0x000000011f853a00 JavaThread "IntelliJ Suspend Helper"    daemon [_thread_blocked, id=32771, stack(0x0000000171bd8000,0x0000000171ddb000) (2060K)]
  0x000000011f861e00 JavaThread "Notification Thread"        daemon [_thread_blocked, id=43011, stack(0x0000000171de4000,0x0000000171fe7000) (2060K)]
  0x00000001400aa000 JavaThread ""                                  [_thread_blocked, id=42755, stack(0x0000000171ff0000,0x00000001721f3000) (2060K)]
  0x000000011f847600 JavaThread ""                                  [_thread_blocked, id=42243, stack(0x00000001721fc000,0x00000001723ff000) (2060K)]
=>0x000000011f903000 JavaThread ""                                  [_thread_in_vm, id=41731, stack(0x0000000172408000,0x000000017260b000) (2060K)]
  0x000000011f808200 JavaThread "DestroyJavaVM"                     [_thread_blocked, id=5379, stack(0x000000016f3dc000,0x000000016f5df000) (2060K)]
  0x000000011f964e00 JavaThread "driver-conductor"                  [_thread_blocked, id=33283, stack(0x0000000172614000,0x0000000172817000) (2060K)]
  0x000000011f96ee00 JavaThread "sender"                            [_thread_blocked, id=33539, stack(0x0000000172820000,0x0000000172a23000) (2060K)]
  0x000000013d810e00 JavaThread "receiver"                          [_thread_blocked, id=40707, stack(0x0000000172a2c000,0x0000000172c2f000) (2060K)]
  0x000000011f9eac00 JavaThread "aeron-client-conductor"            [_thread_blocked, id=35075, stack(0x000000017325c000,0x000000017345f000) (2060K)]
Total: 21

Other Threads:
  0x000000013f708eb0 VMThread "VM Thread"                           [id=17923, stack(0x0000000170230000,0x0000000170433000) (2060K)]
  0x000000011f704970 WatcherThread "VM Periodic Task Thread"        [id=16899, stack(0x0000000170024000,0x0000000170227000) (2060K)]
  0x000000011f606880 WorkerThread "GC Thread#0"                     [id=14083, stack(0x000000016f5e8000,0x000000016f7eb000) (2060K)]
  0x000000013f6263e0 WorkerThread "GC Thread#1"                     [id=39683, stack(0x0000000173674000,0x0000000173877000) (2060K)]
  0x000000013f626950 WorkerThread "GC Thread#2"                     [id=36355, stack(0x0000000173880000,0x0000000173a83000) (2060K)]
  0x000000011f64b320 WorkerThread "GC Thread#3"                     [id=39427, stack(0x0000000173a8c000,0x0000000173c8f000) (2060K)]
  0x000000011f64b890 WorkerThread "GC Thread#4"                     [id=37123, stack(0x0000000173c98000,0x0000000173e9b000) (2060K)]
  0x000000011f64be00 WorkerThread "GC Thread#5"                     [id=37379, stack(0x0000000173ea4000,0x00000001740a7000) (2060K)]
  0x000000011f64c370 WorkerThread "GC Thread#6"                     [id=38659, stack(0x00000001740b0000,0x00000001742b3000) (2060K)]
  0x000000011f64c8e0 WorkerThread "GC Thread#7"                     [id=37891, stack(0x00000001742bc000,0x00000001744bf000) (2060K)]
  0x000000013f605c00 ConcurrentGCThread "G1 Main Marker"            [id=13571, stack(0x000000016f7f4000,0x000000016f9f7000) (2060K)]
  0x000000013f606580 WorkerThread "G1 Conc#0"                       [id=12803, stack(0x000000016fa00000,0x000000016fc03000) (2060K)]
  0x000000013f874400 ConcurrentGCThread "G1 Refine#0"               [id=12291, stack(0x000000016fc0c000,0x000000016fe0f000) (2060K)]
  0x000000013f608960 ConcurrentGCThread "G1 Service"                [id=21507, stack(0x000000016fe18000,0x000000017001b000) (2060K)]
Total: 14

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000007800000000-0x0000007800cf0000-0x0000007800cf0000), size 13565952, SharedBaseAddress: 0x0000007800000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000007801000000-0x0000007841000000, reserved size: 1073741824
Narrow klass base: 0x0000007800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 8 total, 8 available
 Memory: 16384M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 264192K, used 17336K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 1 survivors (2048K)
 Metaspace       used 9186K, committed 9408K, reserved 1114112K
  class space    used 765K, committed 896K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000700000000, 0x0000000700000000, 0x0000000700200000|  0%| F|  |TAMS 0x0000000700000000| PB 0x0000000700000000| Untracked 
|   1|0x0000000700200000, 0x0000000700200000, 0x0000000700400000|  0%| F|  |TAMS 0x0000000700200000| PB 0x0000000700200000| Untracked 
|   2|0x0000000700400000, 0x0000000700400000, 0x0000000700600000|  0%| F|  |TAMS 0x0000000700400000| PB 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700600000, 0x0000000700800000|  0%| F|  |TAMS 0x0000000700600000| PB 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700800000, 0x0000000700a00000|  0%| F|  |TAMS 0x0000000700800000| PB 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700a00000, 0x0000000700c00000|  0%| F|  |TAMS 0x0000000700a00000| PB 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700c00000, 0x0000000700e00000|  0%| F|  |TAMS 0x0000000700c00000| PB 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000700e00000, 0x0000000701000000|  0%| F|  |TAMS 0x0000000700e00000| PB 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701000000, 0x0000000701200000|  0%| F|  |TAMS 0x0000000701000000| PB 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701200000, 0x0000000701400000|  0%| F|  |TAMS 0x0000000701200000| PB 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701400000, 0x0000000701600000|  0%| F|  |TAMS 0x0000000701400000| PB 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701600000, 0x0000000701800000|  0%| F|  |TAMS 0x0000000701600000| PB 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701800000, 0x0000000701a00000|  0%| F|  |TAMS 0x0000000701800000| PB 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701a00000, 0x0000000701c00000|  0%| F|  |TAMS 0x0000000701a00000| PB 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701c00000, 0x0000000701e00000|  0%| F|  |TAMS 0x0000000701c00000| PB 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000701e00000, 0x0000000702000000|  0%| F|  |TAMS 0x0000000701e00000| PB 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702000000, 0x0000000702200000|  0%| F|  |TAMS 0x0000000702000000| PB 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702200000, 0x0000000702400000|  0%| F|  |TAMS 0x0000000702200000| PB 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702400000, 0x0000000702600000|  0%| F|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702600000, 0x0000000702800000|  0%| F|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|  25|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  49|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  50|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  56|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  58|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  59|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  64|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  67|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  86|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
| 116|0x000000070e800000, 0x000000070e9e3398, 0x000000070ea00000| 94%| S|CS|TAMS 0x000000070e800000| PB 0x000000070e800000| Complete 
| 117|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
| 118|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
| 119|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 120|0x000000070f000000, 0x000000070f057b58, 0x000000070f200000| 17%| E|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Complete 
| 121|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| E|CS|TAMS 0x000000070f200000| PB 0x000000070f200000| Complete 
| 122|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| E|CS|TAMS 0x000000070f400000| PB 0x000000070f400000| Complete 
| 123|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%| E|CS|TAMS 0x000000070f600000| PB 0x000000070f600000| Complete 
| 124|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%| E|CS|TAMS 0x000000070f800000| PB 0x000000070f800000| Complete 
| 125|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%| E|CS|TAMS 0x000000070fa00000| PB 0x000000070fa00000| Complete 
| 126|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| E|CS|TAMS 0x000000070fc00000| PB 0x000000070fc00000| Complete 
| 127|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| E|CS|TAMS 0x000000070fe00000| PB 0x000000070fe00000| Complete 
|2047|0x00000007ffe00000, 0x00000007fff0af50, 0x0000000800000000| 52%| O|  |TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Untracked 

Card table byte_map: [0x000000011a83c000,0x000000011b03c000] _byte_map_base: 0x000000011703c000

Marking Bits: (CMBitMap*) 0x000000013f80ba10
 Bits: [0x000000011b03c000, 0x000000011f03c000)

Polling page: 0x0000000100b44000

Metaspace:

Usage:
  Non-class:      8.22 MB used.
      Class:    765.09 KB used.
       Both:      8.97 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       8.31 MB ( 13%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     896.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       9.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  6.75 MB
       Class:  15.04 MB
        Both:  21.79 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 160.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 147.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 361.
num_chunk_merges: 0.
num_chunk_splits: 230.
num_chunks_enlarged: 164.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120032Kb used=677Kb max_used=677Kb free=119354Kb
 bounds [0x0000000113304000, 0x0000000113574000, 0x000000011a83c000]
CodeHeap 'profiled nmethods': size=120016Kb used=2362Kb max_used=2362Kb free=117653Kb
 bounds [0x000000010b83c000, 0x000000010baac000, 0x0000000112d70000]
CodeHeap 'non-nmethods': size=5712Kb used=1416Kb max_used=1459Kb free=4295Kb
 bounds [0x0000000112d70000, 0x0000000112fe0000, 0x0000000113304000]
 total_blobs=1853 nmethods=1309 adapters=453
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 41.411 Thread 0x000000011f83d000 1309       3       java.lang.String::valueOf (15 bytes)
Event: 41.412 Thread 0x000000011f834800 1311       4       java.lang.String::toString (2 bytes)
Event: 41.413 Thread 0x000000011f83d000 nmethod 1309 0x000000010ba87090 code [0x000000010ba87240, 0x000000010ba87490]
Event: 41.413 Thread 0x000000011f83d000 1310       3       java.lang.Integer::getChars (121 bytes)
Event: 41.413 Thread 0x000000011f834800 nmethod 1311 0x00000001133ad410 code [0x00000001133ad580, 0x00000001133ad600]
Event: 41.414 Thread 0x000000011f83d000 nmethod 1310 0x000000010ba87590 code [0x000000010ba87780, 0x000000010ba87bb8]
Event: 41.414 Thread 0x000000011f83d000 1312       3       java.lang.StackTraceElement::length (13 bytes)
Event: 41.414 Thread 0x000000011f83d000 nmethod 1312 0x000000010ba87e10 code [0x000000010ba87fc0, 0x000000010ba88220]
Event: 41.414 Thread 0x000000011f83d000 1313       3       java.lang.StringBuilder::<init> (6 bytes)
Event: 41.414 Thread 0x000000011f83d000 nmethod 1313 0x000000010ba88390 code [0x000000010ba88540, 0x000000010ba886c8]
Event: 45.360 Thread 0x000000011f83d000 1314       3       java.lang.invoke.Invokers::maybeCustomize (5 bytes)
Event: 45.361 Thread 0x000000011f83d000 nmethod 1314 0x000000010ba88790 code [0x000000010ba88940, 0x000000010ba88b10]
Event: 45.361 Thread 0x000000011f83d000 1315       3       java.lang.invoke.MethodHandle::maybeCustomize (38 bytes)
Event: 45.361 Thread 0x000000011f83d000 nmethod 1315 0x000000010ba88c10 code [0x000000010ba88e00, 0x000000010ba89300]
Event: 45.361 Thread 0x000000011f83d000 1316       3       jdk.internal.misc.InternalLock::lock (8 bytes)
Event: 45.362 Thread 0x000000011f83d000 nmethod 1316 0x000000010ba89510 code [0x000000010ba89700, 0x000000010ba89b60]
Event: 45.362 Thread 0x000000011f83d000 1317       3       jdk.internal.misc.InternalLock::unlock (8 bytes)
Event: 45.362 Thread 0x000000011f83d000 nmethod 1317 0x000000010ba89d10 code [0x000000010ba89f00, 0x000000010ba8a340]
Event: 47.389 Thread 0x000000011f83d000 1318       3       sun.invoke.util.Wrapper::isDoubleWord (15 bytes)
Event: 47.389 Thread 0x000000011f83d000 nmethod 1318 0x000000010ba8a510 code [0x000000010ba8a6c0, 0x000000010ba8a830]

GC Heap History (2 events):
Event: 0.724 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 264192K, used 23595K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 0 survivors (0K)
 Metaspace       used 9061K, committed 9344K, reserved 1114112K
  class space    used 753K, committed 896K, reserved 1048576K
}
Event: 0.725 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 264192K, used 3000K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 9061K, committed 9344K, reserved 1114112K
  class space    used 753K, committed 896K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.004 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjava.dylib
Event: 0.004 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libzip.dylib
Event: 0.039 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libinstrument.dylib
Event: 0.059 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnio.dylib
Event: 0.064 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libzip.dylib
Event: 0.091 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnet.dylib
Event: 0.103 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjimage.dylib
Event: 0.113 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libextnet.dylib

Deoptimization events (20 events):
Event: 42.467 Thread 0x000000013a1a7000 DEOPT PACKING pc=0x000000011335589c sp=0x000000017366a830
Event: 42.467 Thread 0x000000013a1a7000 DEOPT UNPACKING pc=0x0000000112db2e7c sp=0x000000017366a4f0 mode 1
Event: 42.468 Thread 0x000000013a808200 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000001133655e0 relative=0x00000000000002a0
Event: 42.468 Thread 0x000000013a808200 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000001133655e0 method=org.agrona.concurrent.AgentRunner.workLoop(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V @ 4 c2
Event: 42.468 Thread 0x000000013a808200 DEOPT PACKING pc=0x00000001133655e0 sp=0x0000000173046890
Event: 42.468 Thread 0x000000013a808200 DEOPT UNPACKING pc=0x0000000112db301c sp=0x0000000173046870 mode 2
Event: 42.469 Thread 0x000000013a22ba00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000001133655e0 relative=0x00000000000002a0
Event: 42.469 Thread 0x000000013a22ba00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000001133655e0 method=org.agrona.concurrent.AgentRunner.workLoop(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V @ 4 c2
Event: 42.469 Thread 0x000000013a22ba00 DEOPT PACKING pc=0x00000001133655e0 sp=0x0000000173252890
Event: 42.469 Thread 0x000000013a22ba00 DEOPT UNPACKING pc=0x0000000112db301c sp=0x0000000173252870 mode 2
Event: 42.469 Thread 0x000000011f964e00 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000000113354e60 relative=0x00000000000000a0
Event: 42.469 Thread 0x000000011f964e00 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000113354e60 method=org.agrona.AbstractMutableDirectBuffer.putLong(IJ)V @ 4 c2
Event: 45.360 Thread 0x000000011f964e00 DEOPT PACKING pc=0x0000000113354e60 sp=0x0000000172816390
Event: 45.360 Thread 0x000000011f964e00 DEOPT UNPACKING pc=0x0000000112db301c sp=0x0000000172816340 mode 2
Event: 45.360 Thread 0x000000011f964e00 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000000113354e60 relative=0x00000000000000a0
Event: 45.360 Thread 0x000000011f964e00 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000113354e60 method=org.agrona.AbstractMutableDirectBuffer.putLong(IJ)V @ 4 c2
Event: 45.360 Thread 0x000000011f964e00 DEOPT PACKING pc=0x0000000113354e60 sp=0x0000000172816390
Event: 45.360 Thread 0x000000011f964e00 DEOPT UNPACKING pc=0x0000000112db301c sp=0x0000000172816340 mode 2
Event: 47.387 Thread 0x000000011f9eac00 DEOPT PACKING pc=0x000000011335589c sp=0x000000017345e830
Event: 47.387 Thread 0x000000011f9eac00 DEOPT UNPACKING pc=0x0000000112db2e7c sp=0x000000017345e4f0 mode 1

Classes loaded (20 events):
Event: 6.848 Loading class java/io/DataOutputStream done
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsShorts$ArrayHandle
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsShorts$ArrayHandle done
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsChars$ArrayHandle
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsChars$ArrayHandle done
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsFloats$ArrayHandle
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsFloats$ArrayHandle done
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsDoubles$ArrayHandle
Event: 6.848 Loading class java/lang/invoke/VarHandleByteArrayAsDoubles$ArrayHandle done
Event: 38.023 Loading class io/aeron/driver/DriverManagedResource
Event: 38.023 Loading class io/aeron/driver/DriverManagedResource done
Event: 41.410 Loading class java/lang/Throwable$WrappedPrintStream
Event: 41.410 Loading class java/lang/Throwable$WrappedPrintStream done
Event: 41.411 Loading class org/agrona/CloseHelper
Event: 41.411 Loading class org/agrona/CloseHelper done
Event: 41.413 Loading class org/agrona/collections/ArrayListUtil
Event: 41.413 Loading class org/agrona/collections/ArrayListUtil done
Event: 45.362 Loading class java/util/IdentityHashMap$EntrySet
Event: 47.388 Loading class java/util/IdentityHashMap$EntrySet done
Event: 47.389 Loading class java/util/IdentityHashMap$EntryIterator

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.052 Thread 0x000000013f708eb0 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 0.102 Thread 0x000000011f903000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f669068}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000070f669068) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.103 Thread 0x000000011f903000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f4043e0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070f4043e0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.104 Thread 0x000000011f903000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f418ac8}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f418ac8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.104 Thread 0x000000011f847600 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7b4938}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f7b4938) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.158 Thread 0x000000011f96ee00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f0714e8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000070f0714e8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.158 Thread 0x000000013d810e00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f153fb0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000070f153fb0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.159 Thread 0x000000011f96ee00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f0799c0}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x000000070f0799c0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.159 Thread 0x000000013d810e00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f158da0}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x000000070f158da0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.162 Thread 0x00000001400aa000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f1e2070}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x000000070f1e2070) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.164 Thread 0x00000001400aa000 Exception <a 'sun/nio/fs/UnixException'{0x000000070f1e95d8}> (0x000000070f1e95d8) 
thrown [src/hotspot/share/prims/jni.cpp, line 520]
Event: 0.164 Thread 0x000000011f964e00 Implicit null exception at 0x0000000113339abc to 0x000000011333a090
Event: 0.189 Thread 0x00000001400aa000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ecf1df0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, java.lang.Object, java.lang.Object)'> (0x000000070ecf1df0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.195 Thread 0x000000011f964e00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070edbbfe0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070edbbfe0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.195 Thread 0x000000011f964e00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070edbfca8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070edbfca8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.195 Thread 0x000000011f964e00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070edc2ec0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070edc2ec0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 39.264 Thread 0x000000013a1a7000 Implicit null exception at 0x000000011339097c to 0x0000000113390a4c
Event: 39.265 Thread 0x000000011f9eac00 Implicit null exception at 0x00000001133909a8 to 0x0000000113390a68
Event: 39.265 Thread 0x000000013a1a7000 Implicit null exception at 0x00000001133a0354 to 0x00000001133a0914
Event: 39.265 Thread 0x000000011f9eac00 Implicit null exception at 0x0000000113395428 to 0x00000001133955e0
Event: 39.265 Thread 0x000000013a1a7000 Implicit null exception at 0x0000000113395428 to 0x00000001133955e0

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 45.376 Executing VM operation: get/set locals
Event: 45.376 Executing VM operation: get/set locals done
Event: 45.376 Executing VM operation: get/set locals
Event: 45.376 Executing VM operation: get/set locals done
Event: 47.387 Executing VM operation: ChangeSingleStep
Event: 47.387 Executing VM operation: ChangeSingleStep done
Event: 47.388 Executing VM operation: ChangeSingleStep
Event: 47.388 Executing VM operation: ChangeSingleStep done
Event: 47.408 Executing VM operation: get/set locals
Event: 47.408 Executing VM operation: get/set locals done
Event: 47.410 Executing VM operation: get/set locals
Event: 47.410 Executing VM operation: get/set locals done
Event: 47.410 Executing VM operation: get/set locals
Event: 47.410 Executing VM operation: get/set locals done
Event: 47.410 Executing VM operation: get/set locals
Event: 47.410 Executing VM operation: get/set locals done
Event: 47.410 Executing VM operation: get/set locals
Event: 47.410 Executing VM operation: get/set locals done
Event: 47.410 Executing VM operation: get/set locals
Event: 47.410 Executing VM operation: get/set locals done

Memory protections (20 events):
Event: 0.034 Protecting memory [0x00000001719cc000,0x00000001719d8000] with protection modes 0
Event: 0.054 Protecting memory [0x0000000171bd8000,0x0000000171be4000] with protection modes 0
Event: 0.055 Protecting memory [0x0000000171de4000,0x0000000171df0000] with protection modes 0
Event: 0.070 Protecting memory [0x0000000171ff0000,0x0000000171ffc000] with protection modes 0
Event: 0.084 Protecting memory [0x00000001721fc000,0x0000000172208000] with protection modes 0
Event: 0.085 Protecting memory [0x0000000172408000,0x0000000172414000] with protection modes 0
Event: 0.085 Protecting memory [0x000000016f3dc000,0x000000016f3e8000] with protection modes 3
Event: 0.085 Protecting memory [0x000000016f3dc000,0x000000016f3e8000] with protection modes 0
Event: 0.158 Protecting memory [0x0000000172614000,0x0000000172620000] with protection modes 0
Event: 0.158 Protecting memory [0x0000000172820000,0x000000017282c000] with protection modes 0
Event: 0.158 Protecting memory [0x0000000172a2c000,0x0000000172a38000] with protection modes 0
Event: 0.197 Protecting memory [0x0000000172c38000,0x0000000172c44000] with protection modes 0
Event: 0.197 Protecting memory [0x0000000172e44000,0x0000000172e50000] with protection modes 0
Event: 0.197 Protecting memory [0x0000000173050000,0x000000017305c000] with protection modes 0
Event: 0.208 Protecting memory [0x000000017325c000,0x0000000173268000] with protection modes 0
Event: 0.209 Protecting memory [0x0000000173468000,0x0000000173474000] with protection modes 0
Event: 42.468 Protecting memory [0x0000000172e44000,0x0000000172e50000] with protection modes 3
Event: 42.469 Protecting memory [0x0000000173050000,0x000000017305c000] with protection modes 3
Event: 47.388 Protecting memory [0x0000000173468000,0x0000000173474000] with protection modes 3
Event: 47.389 Protecting memory [0x0000000172c38000,0x0000000172c44000] with protection modes 3

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.034 Thread 0x000000011f831e00 Thread added: 0x000000011f853200
Event: 0.054 Thread 0x000000011f808200 Thread added: 0x000000011f853a00
Event: 0.055 Thread 0x000000011f808200 Thread added: 0x000000011f861e00
Event: 0.069 Thread 0x000000011f808200 Thread added: 0x00000001400aa000
Event: 0.084 Thread 0x000000011f808200 Thread added: 0x000000011f847600
Event: 0.085 Thread 0x000000011f808200 Thread added: 0x000000011f903000
Event: 0.085 Thread 0x000000011f808200 Thread exited: 0x000000011f808200
Event: 0.085 Thread 0x000000011f808200 Thread added: 0x000000011f808200
Event: 0.158 Thread 0x00000001400aa000 Thread added: 0x000000011f964e00
Event: 0.158 Thread 0x00000001400aa000 Thread added: 0x000000011f96ee00
Event: 0.158 Thread 0x00000001400aa000 Thread added: 0x000000013d810e00
Event: 0.197 Thread 0x00000001400aa000 Thread added: 0x000000013a21f600
Event: 0.197 Thread 0x000000013a21f600 Thread added: 0x000000013a808200
Event: 0.197 Thread 0x000000013a21f600 Thread added: 0x000000013a22ba00
Event: 0.208 Thread 0x000000011f847600 Thread added: 0x000000011f9eac00
Event: 0.209 Thread 0x000000011f903000 Thread added: 0x000000013a1a7000
Event: 42.468 Thread 0x000000013a808200 Thread exited: 0x000000013a808200
Event: 42.469 Thread 0x000000013a22ba00 Thread exited: 0x000000013a22ba00
Event: 47.388 Thread 0x000000013a1a7000 Thread exited: 0x000000013a1a7000
Event: 47.389 Thread 0x000000013a21f600 Thread exited: 0x000000013a21f600


Dynamic libraries:
0x0000000100af0000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjli.dylib
0x00000001aa162000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000191929000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x0000000194c18000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000018efb1000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000019bb95000 	/usr/lib/libSystem.B.dylib
0x0000000192dbb000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000023becb000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00000001a3188000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x0000000199729000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000019e35f000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000019e6b6000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000026a93b000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001f48b1000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000026f9b4000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000026e9fc000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000018ec15000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000019d7c3000 	/usr/lib/libspindump.dylib
0x0000000192f6d000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000019af87000 	/usr/lib/libbsm.0.dylib
0x00000001971ac000 	/usr/lib/libapp_launch_measurement.dylib
0x0000000196555000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00000001971b0000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x0000000198d43000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x0000000199f67000 	/usr/lib/liblangid.dylib
0x000000019972f000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x0000000193993000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x0000000193eb9000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00000001a3867000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000019d606000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x0000000198d20000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x0000000196586000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000019badf000 	/usr/lib/libz.1.dylib
0x00000001a777a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x0000000199714000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000019118e000 	/usr/lib/libicucore.A.dylib
0x000000019f73d000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000019e667000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001ba6d1000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00000001938de000 	/usr/lib/libMobileGestalt.dylib
0x000000019940d000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x0000000196a8d000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x0000000190d83000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00000001a31c4000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x0000000196eb3000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000019064e000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x0000000196674000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000019dc2c000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00000001938dc000 	/usr/lib/libenergytrace.dylib
0x00000001aea77000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00000001917d9000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00000001a35bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000019713d000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001ee281000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00000001971fa000 	/usr/lib/libxml2.2.dylib
0x000000019ae6b000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000018d558000 	/usr/lib/libobjc.A.dylib
0x000000018d865000 	/usr/lib/libc++.1.dylib
0x00000001a353c000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00000001945e9000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000018d9c1000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x0000000199ae9000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000019042f000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001ef7c6000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001efd4a000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001efd4d000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000019976a000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001f5474000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001e1a90000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000019bb9a000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001cd3a4000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000019f0d2000 	/usr/lib/swift/libswiftCore.dylib
0x00000001b6d7a000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001b6dd4000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001b418e000 	/usr/lib/swift/libswiftDarwin.dylib
0x00000002765c9000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00000001a50b3000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001b6dd5000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001c392b000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001d2901000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001a7d20000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00000002765f6000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001c8d52000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001cd395000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001b6d8c000 	/usr/lib/swift/libswiftXPC.dylib
0x00000002766dc000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x00000002766df000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000027683e000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00000002768d1000 	/usr/lib/swift/libswift_errno.dylib
0x00000002768d3000 	/usr/lib/swift/libswift_math.dylib
0x00000002768d6000 	/usr/lib/swift/libswift_signal.dylib
0x00000002768d7000 	/usr/lib/swift/libswift_stdio.dylib
0x00000002768d8000 	/usr/lib/swift/libswift_time.dylib
0x00000001a7d24000 	/usr/lib/swift/libswiftos.dylib
0x00000001ba628000 	/usr/lib/swift/libswiftsimd.dylib
0x00000002768d9000 	/usr/lib/swift/libswiftsys_time.dylib
0x00000002768da000 	/usr/lib/swift/libswiftunistd.dylib
0x000000019bdc2000 	/usr/lib/libcompression.dylib
0x000000019e2bf000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000019d2a5000 	/usr/lib/libate.dylib
0x000000019bb8f000 	/usr/lib/system/libcache.dylib
0x000000019bb4a000 	/usr/lib/system/libcommonCrypto.dylib
0x000000019bb75000 	/usr/lib/system/libcompiler_rt.dylib
0x000000019bb6a000 	/usr/lib/system/libcopyfile.dylib
0x000000018d6b3000 	/usr/lib/system/libcorecrypto.dylib
0x000000018d799000 	/usr/lib/system/libdispatch.dylib
0x000000018d959000 	/usr/lib/system/libdyld.dylib
0x000000019bb85000 	/usr/lib/system/libkeymgr.dylib
0x000000019bb2d000 	/usr/lib/system/libmacho.dylib
0x000000019af60000 	/usr/lib/system/libquarantine.dylib
0x000000019bb82000 	/usr/lib/system/libremovefile.dylib
0x0000000193958000 	/usr/lib/system/libsystem_asl.dylib
0x000000018d648000 	/usr/lib/system/libsystem_blocks.dylib
0x000000018d7e3000 	/usr/lib/system/libsystem_c.dylib
0x000000019bb79000 	/usr/lib/system/libsystem_collections.dylib
0x0000000199f54000 	/usr/lib/system/libsystem_configuration.dylib
0x0000000198cef000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000019b670000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000019145a000 	/usr/lib/system/libsystem_darwin.dylib
0x0000000276a11000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000019bb86000 	/usr/lib/system/libsystem_dnssd.dylib
0x0000000276a15000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000018d7e0000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000018d991000 	/usr/lib/system/libsystem_info.dylib
0x000000019baee000 	/usr/lib/system/libsystem_m.dylib
0x000000018d752000 	/usr/lib/system/libsystem_malloc.dylib
0x00000001938c1000 	/usr/lib/system/libsystem_networkextension.dylib
0x00000001918bc000 	/usr/lib/system/libsystem_notify.dylib
0x0000000199f59000 	/usr/lib/system/libsystem_sandbox.dylib
0x0000000276a1d000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000019bb7e000 	/usr/lib/system/libsystem_secinit.dylib
0x000000018d910000 	/usr/lib/system/libsystem_kernel.dylib
0x000000018d989000 	/usr/lib/system/libsystem_platform.dylib
0x000000018d94c000 	/usr/lib/system/libsystem_pthread.dylib
0x00000001954b2000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000018d697000 	/usr/lib/system/libsystem_trace.dylib
0x000000019bb58000 	/usr/lib/system/libunwind.dylib
0x000000018d64c000 	/usr/lib/system/libxpc.dylib
0x000000018d8f2000 	/usr/lib/libc++abi.dylib
0x00000002755e9000 	/usr/lib/libRosetta.dylib
0x0000000191758000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000019bb62000 	/usr/lib/liboah.dylib
0x000000019bb97000 	/usr/lib/libfakelink.dylib
0x00000001a722d000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001b3cb7000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00000001934f6000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x0000000197174000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x0000000191465000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00000001965e9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000019b677000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000019bce4000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000019542d000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000018df00000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000019d0fa000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x0000000197182000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000019bd79000 	/usr/lib/libapple_nghttp2.dylib
0x000000019508e000 	/usr/lib/libsqlite3.dylib
0x000000019fdb5000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00000001953c4000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00000001954bb000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000019bb31000 	/usr/lib/system/libkxld.dylib
0x0000000237a85000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000275441000 	/usr/lib/libCoreEntitlements.dylib
0x0000000252ec1000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x0000000195073000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000019b657000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000019af6f000 	/usr/lib/libcoretls.dylib
0x000000019d170000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000019bdbc000 	/usr/lib/libpam.2.dylib
0x000000019d1e4000 	/usr/lib/libxar.1.dylib
0x000000019bbec000 	/usr/lib/libarchive.2.dylib
0x00000001a14b7000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000023beef000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000025bee8000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000025cbba000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000027666d000 	/usr/lib/swift/libswiftSystem.dylib
0x0000000199f62000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001b6c65000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00000001a4574000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001f4776000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x0000000197385000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x0000000193424000 	/usr/lib/libboringssl.dylib
0x00000001954a1000 	/usr/lib/libdns_services.dylib
0x00000001b5e19000 	/usr/lib/libquic.dylib
0x000000019f061000 	/usr/lib/libusrtcp.dylib
0x00000001da76b000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x00000002765ca000 	/usr/lib/swift/libswiftDistributed.dylib
0x0000000276663000 	/usr/lib/swift/libswiftSynchronization.dylib
0x00000001934f5000 	/usr/lib/libnetwork.dylib
0x00000001c7991000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000019bd54000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001a5849000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001d6437000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00000001a5825000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000027530c000 	/usr/lib/libAppleArchive.dylib
0x000000019b663000 	/usr/lib/libbz2.1.0.dylib
0x000000019d151000 	/usr/lib/liblzma.5.dylib
0x000000019ac5f000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001b6cee000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000019d33f000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000018ea0c000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000024b211000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001a534b000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000019ae97000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000019aff9000 	/usr/lib/libgermantok.dylib
0x000000019a086000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00000001952df000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001a5418000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000019ae88000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001a7068000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000019913a000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x0000000193970000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001aac03000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000019dc2a000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000018ff56000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x0000000198fde000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x0000000198d39000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00000001972e4000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000019bdba000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000025b239000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000019dc72000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x0000000199f60000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000019d172000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000019d1f3000 	/usr/lib/libutil.dylib
0x00000002660f8000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000019667d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00000001a3596000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000019d22b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000018e378000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000019f98c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000019479f000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000019e248000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000019fd7f000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000019fd76000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000019f95e000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000019ac2c000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00000002422ea000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000019d778000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x0000000196e61000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00000002759cd000 	/usr/lib/libhvf.dylib
0x0000000257147000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x0000000276613000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000027679b000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000019daf3000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000019d594000 	/usr/lib/libexpat.1.dylib
0x000000019e11e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000019e149000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000019e233000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000019db39000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000019e1da000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000019e1d1000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000246fb8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00000002423b5000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001ee273000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x0000000242f2a000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00000002422eb000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00000001a8e69000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x000000025a3a8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000019d76c000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001ee2d1000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001ee295000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001ee45d000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001ee29e000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001ee292000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001ee27b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x0000000199ea4000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000019b5c2000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000019b011000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000019b448000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000019b25d000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000019b47a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001f1b68000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001f1b4a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000018e1f3000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001bb9ce000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001c9147000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001b6d55000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000019e205000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000019fd0a000 	/usr/lib/libcups.2.dylib
0x000000019fda4000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000019fa0a000 	/usr/lib/libresolv.9.dylib
0x000000019bbd1000 	/usr/lib/libiconv.2.dylib
0x000000019d7ca000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001a7c77000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000019d5af000 	/usr/lib/libheimdal-asn1.dylib
0x0000000197147000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000019fe07000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x0000000197155000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000019bb2c000 	/usr/lib/libcharset.1.dylib
0x00000001ee7f9000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001b043d000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000024eb40000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000019f945000 	/usr/lib/libAudioStatistics.dylib
0x0000000198fb6000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x0000000190077000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001a9f8f000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000019f8e8000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00000001a121f000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000019d699000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x0000000268bb3000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000023c9ac000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001cda67000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001b6d59000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000019d7b9000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000019fd88000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001b5f12000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000019fc29000 	/usr/lib/libSMC.dylib
0x000000019e0e8000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000019fd96000 	/usr/lib/libperfcheck.dylib
0x0000000238842000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001da532000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001b6d20000 	/usr/lib/libmis.dylib
0x000000019f6f6000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000019e239000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001efe9a000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001a5956000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000019d493000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00000001a447d000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001a7c78000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000019acf0000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000023f950000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00000001a38b7000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001bf36a000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000027528a000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001b04f9000 	/usr/lib/libAccessibility.dylib
0x000000019ab3a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000019be98000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000019affc000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000019bd93000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000019be93000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000019a08d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000018eb19000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00000002502b1000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000019e1cc000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000019e1ac000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000019e1d4000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001da939000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001af718000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000026c3ef000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000019d54b000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001a54c5000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000019ff7a000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001a4665000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00000001a45b0000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000019fd69000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x0000000197345000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000019d5b9000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000019d336000 	/usr/lib/libIOReport.dylib
0x0000000238452000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000027564c000 	/usr/lib/libTLE.dylib
0x00000001eb2cf000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000019af99000 	/usr/lib/libmecab.dylib
0x000000018eca9000 	/usr/lib/libCRFSuite.dylib
0x0000000199f69000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000019bd4c000 	/usr/lib/libThaiTokenizer.dylib
0x000000019af63000 	/usr/lib/libCheckFix.dylib
0x0000000196588000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000024baf3000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x0000000191798000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001c9251000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000019d1f7000 	/usr/lib/libxslt.1.dylib
0x000000019af26000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000001a73eb000 	/usr/lib/libcurl.4.dylib
0x0000000275880000 	/usr/lib/libcrypto.46.dylib
0x00000002763a8000 	/usr/lib/libssl.48.dylib
0x00000001a70c4000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001a7100000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000019fa27000 	/usr/lib/libsasl2.2.dylib
0x00000001b418d000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001a3ee5000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001ead59000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001d6beb000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000025ccb1000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00000001a31b1000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000101f58000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/server/libjvm.dylib
0x0000000100b58000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjimage.dylib
0x0000000100bb4000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjdwp.dylib
0x0000000100bfc000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjava.dylib
0x0000000100b84000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libinstrument.dylib
0x0000000100c94000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libzip.dylib
0x000000027527c000 	/usr/lib/i18n/libiconv_std.dylib
0x0000000275272000 	/usr/lib/i18n/libUTF8.dylib
0x0000000275281000 	/usr/lib/i18n/libmapper_none.dylib
0x0000000100c80000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libdt_socket.dylib
0x0000000100d44000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnio.dylib
0x0000000100d64000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnet.dylib
0x0000000100d24000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libextnet.dylib


VM Arguments:
jvm_args: -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true --add-opens=java.base/sun.nio.ch=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=n,suspend=y,address=127.0.0.1:64583 -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2025.1/captureAgent/debugger-agent.jar=file:///var/folders/1q/nff07fl14ss1md1tvlg_hj540000gn/T/capture12551269003144505367.props -Dfile.encoding=UTF-8 -Duser.country=TW -Duser.language=en -Duser.variant 
java_command: org.example.App
java_class_path (initial): /Users/<USER>/Documents/Projects/simple-sequencer/exchange/build/classes/java/main:/Users/<USER>/Documents/Projects/simple-sequencer/exchange/build/resources/main:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.aeron/aeron-all/1.42.1/782f6e43148542cd0b122004a562d658403bc360/aeron-all-1.42.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/uk.co.real-logic/sbe-tool/1.30.0/b89953357e008b144321f607ad4606de47e0279a/sbe-tool-1.30.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.5.18/fc371f3fc97a639de2d67947cffb7518ec5e3d40/logback-classic-1.5.18.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-api/2.0.17/d9e58ac9c7779ba3bf8142aff6c830617a7fe60f/slf4j-api-2.0.17.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.agrona/agrona/1.20.0/580b67864f7739bf7778162f418ada69fa3037/agrona-1.20.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.5.18/6c0375624f6f36b4e089e2488ba21334a11ef13f/logback-core-1.5.18.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {ergonomic}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839564                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909338                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909338                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=/Users/<USER>/.sdkman/candidates/java/current
PATH=/Users/<USER>/.nvm/versions/node/v22.0.0/bin:/Users/<USER>/.sdkman/candidates/java/current/bin:/Users/<USER>/.sdkman/candidates/groovy/current/bin:/Users/<USER>/.sdkman/candidates/gradle/current/bin:/opt/homebrew/anaconda3/bin:/Users/<USER>/bin:/usr/local/bin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts
SHELL=/bin/zsh
LC_CTYPE=en_US.UTF-8
TMPDIR=/var/folders/1q/nff07fl14ss1md1tvlg_hj540000gn/T/

Active Locale:
LC_ALL=C/en_US.UTF-8/C/C/C/C
LC_COLLATE=C
LC_CTYPE=en_US.UTF-8
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T8112 arm64
OS uptime: 2 days 1:18 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 1048576/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 1.64 2.06 2.78

CPU: total 8 (initial active 8) 0x61:0x0:0xda33d83d:0, fp, asimd, aes, pmull, sha1, sha256, crc32, lse, sha3, sha512
machdep.cpu.brand_string:Apple M2
hw.cachelinesize:128
hw.l1icachesize:131072
hw.l1dcachesize:65536
hw.l2cachesize:4194304

Memory: 16k page, physical 16777216k(192576k free), swap 0k(0k free)

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for bsd-aarch64 JRE (21.0.6+7-LTS) (Zulu21.40+17-CA), built on 2025-01-07T13:00:40Z by "tester" with clang Apple LLVM 14.0.3 (clang-1403.0.22.14.1)

END.
