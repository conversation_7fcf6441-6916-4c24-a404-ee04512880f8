#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x000000010d941660, pid=73872, tid=42499
#
# JRE version: OpenJDK Runtime Environment Zulu21.40+17-CA (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Zulu21.40+17-CA (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# J 1124 c1 org.agrona.AbstractMutableDirectBuffer.getLong(ILjava/nio/ByteOrder;)J (45 bytes) @ 0x000000010d941660 [0x000000010d941480+0x00000000000001e0]
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://www.azul.com/support/
#

---------------  S U M M A R Y ------------

Command Line: -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true --add-opens=java.base/sun.nio.ch=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=n,suspend=y,address=127.0.0.1:64378 -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2025.1/captureAgent/debugger-agent.jar=file:///var/folders/1q/nff07fl14ss1md1tvlg_hj540000gn/T/capture16584118917623686056.props -Dfile.encoding=UTF-8 -Duser.country=TW -Duser.language=en -Duser.variant org.example.App

Host: "Mac14,2" arm64, 8 cores, 16G, Darwin 24.5.0, macOS 15.5 (24F74)
Time: Sun Jun 15 21:06:28 2025 CST elapsed time: 242.893729 seconds (0d 0h 4m 2s)

---------------  T H R E A D  ---------------

Current thread (0x000000012a808200):  JavaThread ""                 [_thread_in_Java, id=42499, stack(0x0000000300628000,0x000000030082b000) (2060K)]

Stack: [0x0000000300628000,0x000000030082b000],  sp=0x0000000300829ef0,  free space=2055k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
J 1124 c1 org.agrona.AbstractMutableDirectBuffer.getLong(ILjava/nio/ByteOrder;)J (45 bytes) @ 0x000000010d941660 [0x000000010d941480+0x00000000000001e0]
J 1127 c1 org.example.sbe.MessageHeaderDecoder.sequence()J (20 bytes) @ 0x000000010d9423dc [0x000000010d942240+0x000000000000019c]
v  ~StubRoutines::call_stub 0x0000000114c90154
V  [libjvm.dylib+0x4ba9a4]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x3e0
V  [libjvm.dylib+0x527c08]  jni_invoke_nonstatic(JNIEnv_*, JavaValue*, _jobject*, JNICallType, _jmethodID*, JNI_ArgumentPusher*, JavaThread*)+0x3d0
V  [libjvm.dylib+0x52c01c]  jni_CallLongMethodA+0x118
C  [libjdwp.dylib+0x1b858]  invoker_doInvoke+0xaf4
C  [libjdwp.dylib+0x174d0]  event_callback+0x55c
C  [libjdwp.dylib+0x13a28]  cbBreakpoint+0x158
V  [libjvm.dylib+0x68567c]  JvmtiExport::post_raw_breakpoint(JavaThread*, Method*, unsigned char*)+0x3f4
V  [libjvm.dylib+0x4b1348]  InterpreterRuntime::_breakpoint(JavaThread*, Method*, unsigned char*)+0x58
j  org.example.services.PositionNodeApp.publishOrderEvent()I+97
j  org.example.services.PositionNodeApp.doWork()I+38
J 1210% c2 org.agrona.concurrent.AgentRunner.workLoop(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V (17 bytes) @ 0x0000000115298444 [0x0000000115298300+0x0000000000000144]
j  org.agrona.concurrent.AgentRunner.run()V+63
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.6
j  java.lang.Thread.run()V+19 java.base@21.0.6
v  ~StubRoutines::call_stub 0x0000000114c90154
V  [libjvm.dylib+0x4ba9a4]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x3e0
V  [libjvm.dylib+0x4b98d4]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0x140
V  [libjvm.dylib+0x4b99a0]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, JavaThread*)+0x64
V  [libjvm.dylib+0x58b66c]  thread_entry(JavaThread*, JavaThread*)+0x9c
V  [libjvm.dylib+0x4cf0d4]  JavaThread::thread_main_inner()+0x98
V  [libjvm.dylib+0xa09d20]  Thread::call_run()+0xc8
V  [libjvm.dylib+0x82c57c]  thread_native_entry(Thread*)+0x118
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88

siginfo: si_signo: 11 (SIGSEGV), si_code: 2 (SEGV_ACCERR), si_addr: 0x000000035a018008

Registers:
 x0=0x0000000000000000  x1=0x000000070fea45b0  x2=0x0000000002000008  x3=0x000000070e89bb88
 x4=0x000000035a018008  x5=0x00000001235f04b0  x6=0x000000000001e682  x7=0x000000012a808200
 x8=0x0000000000003861  x9=0x0000000000000001 x10=0x0000000002000010 x11=0x0000000004000000
x12=0x00000001234465b0 x13=0x00000000000007fd x14=0x00000000a8a27032 x15=0x00000000a8826ffb
x16=0x000000018d9539d4 x17=0x0000600003a5d710 x18=0x0000000000000000 x19=0x0000000300829fd0
x20=0x0000000300829fd8 x21=0x000000030082a1d0 x22=0x000000030082a1e0 x23=0x0000000114cd0b00
x24=0x000000000000000b x25=0x000000030082a1e8 x26=0x000000030082a308 x27=0x0000000000000000
x28=0x000000012a808200  fp=0x000000030082a0c0  lr=0x000000010d941578  sp=0x0000000300829ef0
pc=0x000000010d941660 cpsr=0x0000000020001000

Register to memory mapping:

x0 =0x0 is null
x1 =0x000000070fea45b0 is an oop: org.agrona.concurrent.UnsafeBuffer 
{0x000000070fea45b0} - klass: 'org/agrona/concurrent/UnsafeBuffer'
 - ---- fields (total size 5 words):
 - protected 'capacity' 'I' @12  67108864 (0x04000000)
 - protected 'addressOffset' 'J' @16  14361395200 (0x0000000358018000)
 - protected 'byteArray' '[B' @24  null (0x00000000)
 - private 'wrapAdjustment' 'I' @28  0 (0x00000000)
 - private 'byteBuffer' 'Ljava/nio/ByteBuffer;' @32  a 'java/nio/DirectByteBuffer'{0x000000070fea45d8} (0xe1fd48bb)
x2 =0x0000000002000008 is an unknown value
x3 =0x000000070e89bb88 is an oop: java.nio.ByteOrder 
{0x000000070e89bb88} - klass: 'java/nio/ByteOrder'
 - ---- fields (total size 2 words):
 - private final 'name' 'Ljava/lang/String;' @12  "LITTLE_ENDIAN"{0x00000007ffe038d0} (0xfffc071a)
x4 =0x000000035a018008 is an unknown value
x5 =0x00000001235f04b0 is pointing into metadata
x6 =0x000000000001e682 is an unknown value
x7 =0x000000012a808200 is a thread
x8 =0x0000000000003861 is an unknown value
x9 =0x0000000000000001 is an unknown value
x10=0x0000000002000010 is an unknown value
x11=0x0000000004000000 is an unknown value
x12={method} {0x00000001234465b0} 'sequence' '()J' in 'org/example/sbe/MessageHeaderDecoder'
x13=0x00000000000007fd is an unknown value
x14=0x00000000a8a27032 is an unknown value
x15=0x00000000a8826ffb is an unknown value
x16=0x000000018d9539d4: pthread_jit_write_protect_np+0 in /usr/lib/system/libsystem_pthread.dylib at 0x000000018d94c000
x17=0x0000600003a5d710 points into unknown readable memory: 0x00000000ffffffff | ff ff ff ff 00 00 00 00
x18=0x0 is null
x19=0x0000000300829fd0 is pointing into the stack for thread: 0x000000012a808200
x20=0x0000000300829fd8 is pointing into the stack for thread: 0x000000012a808200
x21=0x000000030082a1d0 is pointing into the stack for thread: 0x000000012a808200
x22=0x000000030082a1e0 is pointing into the stack for thread: 0x000000012a808200
x23=0x0000000114cd0b00 is at code_begin+0 in 
[CodeBlob (0x0000000114cd0a90)]
Framesize: 0
BufferBlob (0x0000000114cd0a90) used for I2C/C2I adapters
x24=0x000000000000000b is an unknown value
x25=0x000000030082a1e8 is pointing into the stack for thread: 0x000000012a808200
x26=0x000000030082a308 is pointing into the stack for thread: 0x000000012a808200
x27=0x0 is null
x28=0x000000012a808200 is a thread
 fp=0x000000030082a0c0 is pointing into the stack for thread: 0x000000012a808200
 lr=0x000000010d941578 is at entry_point+248 in (nmethod*)0x000000010d941290
Compiled method (c1) 242898 1124       3       org.agrona.AbstractMutableDirectBuffer::getLong (45 bytes)
 total in heap  [0x000000010d941290,0x000000010d9418e0] = 1616
 relocation     [0x000000010d9413e0,0x000000010d941458] = 120
 main code      [0x000000010d941480,0x000000010d941750] = 720
 stub code      [0x000000010d941750,0x000000010d9417c8] = 120
 oops           [0x000000010d9417c8,0x000000010d9417e0] = 24
 metadata       [0x000000010d9417e0,0x000000010d941820] = 64
 scopes data    [0x000000010d941820,0x000000010d941878] = 88
 scopes pcs     [0x000000010d941878,0x000000010d9418d8] = 96
 dependencies   [0x000000010d9418d8,0x000000010d9418e0] = 8
 sp=0x0000000300829ef0 is pointing into the stack for thread: 0x000000012a808200

Top of Stack: (sp=0x0000000300829ef0)
0x0000000300829ef0:   000000011529846c 0000000115298110
0x0000000300829f00:   0000000000000000 0000000000000000
0x0000000300829f10:   0000000000000001 000000030082a890
0x0000000300829f20:   000000030082a400 000000030082a790
0x0000000300829f30:   00000001234264c8 000000030082a798
0x0000000300829f40:   000000030082a498 000000030082a7a0
0x0000000300829f50:   000000070fea45b0 0000000102000008
0x0000000300829f60:   000000070e89bb88 0000000000000000
0x0000000300829f70:   000000010430d5f0 000000018d955790
0x0000000300829f80:   000000030082a0c0 000000010d9423dc
0x0000000300829f90:   000000a002b57cf8 0000000000000000
0x0000000300829fa0:   0000000000000001 000060000062cc90
0x0000000300829fb0:   000000070e8a70c0 0000600000a726d0
0x0000000300829fc0:   000000030082a0c0 0000000114c90154
0x0000000300829fd0:   0000000000000006 000000070e8a70c0
0x0000000300829fe0:   0000000000000000 000000012a808780
0x0000000300829ff0:   0000000000000000 0000000000000000
0x000000030082a000:   0000000000000000 0000000000000000
0x000000030082a010:   0000000000000000 0000000000000000
0x000000030082a020:   0000000000000000 0000000000000000
0x000000030082a030:   0000000104b8e000 0000000000000000
0x000000030082a040:   000000030082a308 000000030082a1e8
0x000000030082a050:   000000000000000b 0000000114cd0b00
0x000000030082a060:   000000030082a1e0 000000030082a1d0
0x000000030082a070:   000000030082a300 000000012a808200
0x000000030082a080:   000000030082a110 000000030082a308
0x000000030082a090:   000000000000000b 00000001234465b0
0x000000030082a0a0:   0000000114cd0b00 000000030082a1e8
0x000000030082a0b0:   0000000300000001 000000012a808200
0x000000030082a0c0:   000000030082a1a0 00000001043329a4
0x000000030082a0d0:   434f4e4501d505c0 000000012a808200
0x000000030082a0e0:   0000600001d505c0 0000600003744000 

Instructions: (pc=0x000000010d941660)
0x000000010d941560:   e0 03 02 aa e2 03 00 aa e3 03 1d 32 e5 03 01 aa
0x000000010d941570:   e1 03 05 aa 47 8c e4 95 1f 20 03 d5 1f 5d 80 f2
0x000000010d941580:   1f 00 80 f2 e3 3b 40 f9 e2 6b 40 b9 e1 33 40 f9
0x000000010d941590:   20 18 40 b9 00 f0 7d d3 24 08 40 f9 05 20 89 d2
0x000000010d9415a0:   25 70 a4 f2 25 00 c0 f2 a5 00 06 91 e6 03 00 aa
0x000000010d9415b0:   46 00 00 b5 17 00 00 14 06 62 84 d2 06 00 a0 f2
0x000000010d9415c0:   06 14 c0 f2 a9 04 40 f9 c6 00 09 ca c8 f4 7e 92
0x000000010d9415d0:   08 02 00 b4 e6 01 08 37 a9 01 00 b4 3f 05 00 f1
0x000000010d9415e0:   60 01 00 54 bf 39 03 d5 c6 00 09 ca a9 04 40 f9
0x000000010d9415f0:   c6 00 09 ca c8 f4 7e 92 c8 00 00 b4 a9 04 40 f9
0x000000010d941600:   29 01 7f b2 a9 04 00 f9 02 00 00 14 a6 04 00 f9
0x000000010d941610:   05 5a 89 d2 a5 d0 a1 f2 e5 00 c0 f2 06 20 89 d2
0x000000010d941620:   26 70 a4 f2 26 00 c0 f2 c8 a4 40 f9 08 05 00 91
0x000000010d941630:   c8 a4 00 f9 45 7c 40 93 84 00 05 8b 05 96 80 d2
0x000000010d941640:   e5 6b a4 f2 25 00 c0 f2 a6 ac 40 b9 c6 08 00 11
0x000000010d941650:   a6 ac 00 b9 c6 4c 1f 12 df 00 00 71 80 05 00 54
0x000000010d941660:   05 68 64 f8 00 71 97 d2 20 d1 a1 f2 e0 00 c0 f2
0x000000010d941670:   7f 00 00 eb 00 20 89 d2 20 70 a4 f2 20 00 c0 f2
0x000000010d941680:   08 33 80 d2 09 35 80 d2 04 01 89 9a 06 68 64 f8
0x000000010d941690:   c6 04 00 91 06 68 24 f8 c0 01 00 54 00 20 89 d2
0x000000010d9416a0:   20 70 a4 f2 20 00 c0 f2 08 dc 40 f9 08 05 00 91
0x000000010d9416b0:   08 dc 00 f9 e1 03 05 aa e5 3f 00 f9 31 2c ce 95
0x000000010d9416c0:   1f 20 03 d5 1f 86 80 f2 1f 20 80 f2 e5 03 00 aa
0x000000010d9416d0:   e0 03 05 aa fd 7b 49 a9 ff 83 02 91 88 2b 42 f9
0x000000010d9416e0:   ff 63 28 eb 48 02 00 54 c0 03 5f d6 08 5d 88 d2
0x000000010d9416f0:   a8 68 a4 f2 28 00 c0 f2 e8 07 00 f9 08 00 80 92
0x000000010d941700:   e8 03 00 f9 df b7 d1 95 89 ff ff 17 08 00 89 d2
0x000000010d941710:   a8 64 a4 f2 28 00 c0 f2 e8 07 00 f9 08 00 80 92
0x000000010d941720:   e8 03 00 f9 d7 b7 d1 95 ce ff ff 17 88 fd ff 10
0x000000010d941730:   88 37 02 f9 73 44 ce 15 80 83 42 f9 9f 83 02 f9
0x000000010d941740:   9f 87 02 f9 fd 7b 49 a9 ff 83 02 91 cd 9d d1 15
0x000000010d941750:   48 00 00 58 00 01 1f d6 90 46 26 15 01 00 00 00 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x000000011529846c is at entry_point+364 in (nmethod*)0x0000000115298110
Compiled method (c2) 242900 1210 %     4       org.agrona.concurrent.AgentRunner::workLoop @ 0 (17 bytes)
 total in heap  [0x0000000115298110,0x00000001152989d0] = 2240
 relocation     [0x0000000115298260,0x00000001152982f8] = 152
 main code      [0x0000000115298300,0x00000001152986a0] = 928
 stub code      [0x00000001152986a0,0x0000000115298740] = 160
 oops           [0x0000000115298740,0x0000000115298748] = 8
 metadata       [0x0000000115298748,0x00000001152987a8] = 96
 scopes data    [0x00000001152987a8,0x0000000115298860] = 184
 scopes pcs     [0x0000000115298860,0x0000000115298930] = 208
 dependencies   [0x0000000115298930,0x0000000115298948] = 24
 handler table  [0x0000000115298948,0x00000001152989c0] = 120
 nul chk table  [0x00000001152989c0,0x00000001152989d0] = 16
stack at sp + 1 slots: 0x0000000115298110 is at entry_point+-496 in (nmethod*)0x0000000115298110
Compiled method (c2) 242900 1210 %     4       org.agrona.concurrent.AgentRunner::workLoop @ 0 (17 bytes)
 total in heap  [0x0000000115298110,0x00000001152989d0] = 2240
 relocation     [0x0000000115298260,0x00000001152982f8] = 152
 main code      [0x0000000115298300,0x00000001152986a0] = 928
 stub code      [0x00000001152986a0,0x0000000115298740] = 160
 oops           [0x0000000115298740,0x0000000115298748] = 8
 metadata       [0x0000000115298748,0x00000001152987a8] = 96
 scopes data    [0x00000001152987a8,0x0000000115298860] = 184
 scopes pcs     [0x0000000115298860,0x0000000115298930] = 208
 dependencies   [0x0000000115298930,0x0000000115298948] = 24
 handler table  [0x0000000115298948,0x00000001152989c0] = 120
 nul chk table  [0x00000001152989c0,0x00000001152989d0] = 16
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: 0x0 is null
stack at sp + 4 slots: 0x0000000000000001 is an unknown value
stack at sp + 5 slots: 0x000000030082a890 is pointing into the stack for thread: 0x000000012a808200
stack at sp + 6 slots: 0x000000030082a400 is pointing into the stack for thread: 0x000000012a808200
stack at sp + 7 slots: 0x000000030082a790 is pointing into the stack for thread: 0x000000012a808200


Compiled method (c1) 242902 1124       3       org.agrona.AbstractMutableDirectBuffer::getLong (45 bytes)
 total in heap  [0x000000010d941290,0x000000010d9418e0] = 1616
 relocation     [0x000000010d9413e0,0x000000010d941458] = 120
 main code      [0x000000010d941480,0x000000010d941750] = 720
 stub code      [0x000000010d941750,0x000000010d9417c8] = 120
 oops           [0x000000010d9417c8,0x000000010d9417e0] = 24
 metadata       [0x000000010d9417e0,0x000000010d941820] = 64
 scopes data    [0x000000010d941820,0x000000010d941878] = 88
 scopes pcs     [0x000000010d941878,0x000000010d9418d8] = 96
 dependencies   [0x000000010d9418d8,0x000000010d9418e0] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer'
  # this:     c_rarg1:c_rarg1 
                        = 'org/agrona/AbstractMutableDirectBuffer'
  # parm0:    c_rarg2   = int
  # parm1:    c_rarg3:c_rarg3 
                        = 'java/nio/ByteOrder'
  #           [sp+0xa0]  (sp of caller)
  0x000000010d941480: 2808 40b9 | 3f01 086b | c001 0054 

  0x000000010d94148c: ;   {runtime_call ic_miss_stub}
  0x000000010d94148c: 7d2a ce15 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 
  0x000000010d9414ac: 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 
[Verified Entry Point]
  0x000000010d9414c0: 1f20 03d5 | e953 40d1 | 3f01 00f9 | ff83 02d1 | fd7b 09a9 | 2801 0018 | 8923 40b9 | 1f01 09eb 
  0x000000010d9414e0: e000 0054 | 0830 8bd2 | a899 a2f2 | 2800 c0f2 | 0001 3fd6 | 0200 0014 | 0100 0000 | e133 00f9 
  0x000000010d941500: e26b 00b9 | e33b 00f9 

  0x000000010d941508: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d941508: 0020 89d2 | 2070 a4f2 | 2000 c0f2 | 04ac 40b9 | 8408 0011 | 04ac 00b9 | 8424 1f12 | 9f00 0071 
  0x000000010d941528: 200e 0054 

  0x000000010d94152c: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d94152c: 0020 89d2 | 2070 a4f2 | 2000 c0f2 | 0480 40f9 | 8404 0091 | 0480 00f9 | e003 01aa 

  0x000000010d941548: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d941548: 0420 89d2 | 2470 a4f2 | 2400 c0f2 | 8888 40f9 | 0805 0091 | 8888 00f9 | e003 02aa | e203 00aa 
  0x000000010d941568: e303 1d32 | e503 01aa | e103 05aa 

  0x000000010d941574: ; ImmutableOopMap {[96]=Oop [112]=Oop }
                      ;*invokevirtual boundsCheck0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.AbstractMutableDirectBuffer::getLong@10 (line 129)
                      ;   {optimized virtual_call}
  0x000000010d941574: 478c e495 

  0x000000010d941578: ;   {other}
  0x000000010d941578: 1f20 03d5 | 1f5d 80f2 | 1f00 80f2 | e33b 40f9 | e26b 40b9 | e133 40f9 | 2018 40b9 | 00f0 7dd3 
  0x000000010d941598: 2408 40f9 

  0x000000010d94159c: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d94159c: 0520 89d2 | 2570 a4f2 | 2500 c0f2 | a500 0691 | e603 00aa | 4600 00b5 | 1700 0014 

  0x000000010d9415b8: ;   {metadata({type array byte})}
  0x000000010d9415b8: 0662 84d2 | 0600 a0f2 | 0614 c0f2 | a904 40f9 | c600 09ca | c8f4 7e92 | 0802 00b4 | e601 0837 
  0x000000010d9415d8: a901 00b4 | 3f05 00f1 | 6001 0054 | bf39 03d5 | c600 09ca | a904 40f9 | c600 09ca | c8f4 7e92 
  0x000000010d9415f8: c800 00b4 | a904 40f9 | 2901 7fb2 | a904 00f9 | 0200 0014 | a604 00f9 

  0x000000010d941610: ;   {oop(a 'sun/misc/Unsafe'{0x000000070e854ad0})}
  0x000000010d941610: 055a 89d2 | a5d0 a1f2 | e500 c0f2 

  0x000000010d94161c: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d94161c: 0620 89d2 | 2670 a4f2 | 2600 c0f2 | c8a4 40f9 | 0805 0091 | c8a4 00f9 | 457c 4093 | 8400 058b 
  0x000000010d94163c: ;   {metadata(method data for {method} {0x0000000123254800} 'getLong' '(Ljava/lang/Object;J)J' in 'sun/misc/Unsafe')}
  0x000000010d94163c: 0596 80d2 | e56b a4f2 | 2500 c0f2 | a6ac 40b9 | c608 0011 | a6ac 00b9 | c64c 1f12 | df00 0071 
  0x000000010d94165c: 8005 0054 | 0568 64f8 

  0x000000010d941664: ;   {oop(a 'java/nio/ByteOrder'{0x000000070e89bb88})}
  0x000000010d941664: 0071 97d2 | 20d1 a1f2 | e000 c0f2 | 7f00 00eb 

  0x000000010d941674: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d941674: 0020 89d2 | 2070 a4f2 | 2000 c0f2 | 0833 80d2 | 0935 80d2 | 0401 899a | 0668 64f8 | c604 0091 
  0x000000010d941694: 0668 24f8 | c001 0054 

  0x000000010d94169c: ;   {metadata(method data for {method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d94169c: 0020 89d2 | 2070 a4f2 | 2000 c0f2 | 08dc 40f9 | 0805 0091 | 08dc 00f9 | e103 05aa | e53f 00f9 
  0x000000010d9416bc: ; ImmutableOopMap {[96]=Oop [112]=Oop }
                      ;*invokestatic reverseBytes {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.AbstractMutableDirectBuffer::getLong@39 (line 135)
                      ;   {static_call}
  0x000000010d9416bc: 312c ce95 

  0x000000010d9416c0: ;   {other}
  0x000000010d9416c0: 1f20 03d5 | 1f86 80f2 | 1f20 80f2 | e503 00aa | e003 05aa | fd7b 49a9 | ff83 0291 

  0x000000010d9416dc: ;   {poll_return}
  0x000000010d9416dc: 882b 42f9 | ff63 28eb | 4802 0054 | c003 5fd6 

  0x000000010d9416ec: ;   {metadata({method} {0x00000001234542e8} 'getLong' '(ILjava/nio/ByteOrder;)J' in 'org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d9416ec: 085d 88d2 | a868 a4f2 | 2800 c0f2 | e807 00f9 | 0800 8092 | e803 00f9 

  0x000000010d941704: ; ImmutableOopMap {c_rarg1=Oop c_rarg3=Oop [96]=Oop [112]=Oop }
                      ;*synchronization entry
                      ; - org.agrona.AbstractMutableDirectBuffer::getLong@-1 (line 127)
                      ;   {runtime_call counter_overflow Runtime1 stub}
  0x000000010d941704: dfb7 d195 | 89ff ff17 

  0x000000010d94170c: ;   {metadata({method} {0x0000000123254800} 'getLong' '(Ljava/lang/Object;J)J' in 'sun/misc/Unsafe')}
  0x000000010d94170c: 0800 89d2 | a864 a4f2 | 2800 c0f2 | e807 00f9 | 0800 8092 | e803 00f9 

  0x000000010d941724: ; ImmutableOopMap {c_rarg1=Oop c_rarg3=Oop c_rarg0=Oop [96]=Oop [112]=Oop }
                      ;*synchronization entry
                      ; - sun.misc.Unsafe::getLong@-1 (line 267)
                      ; - org.agrona.AbstractMutableDirectBuffer::getLong@27 (line 132)
                      ;   {runtime_call counter_overflow Runtime1 stub}
  0x000000010d941724: d7b7 d195 | ceff ff17 

  0x000000010d94172c: ;   {internal_word}
  0x000000010d94172c: 88fd ff10 | 8837 02f9 

  0x000000010d941734: ;   {runtime_call SafepointBlob}
  0x000000010d941734: 7344 ce15 | 8083 42f9 | 9f83 02f9 | 9f87 02f9 | fd7b 49a9 | ff83 0291 

  0x000000010d94174c: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000000010d94174c: cd9d d115 
[Stub Code]
  0x000000010d941750: ;   {no_reloc}
  0x000000010d941750: 4800 0058 | 0001 1fd6 | 9046 2615 | 0100 0000 

  0x000000010d941760: ;   {trampoline_stub}
  0x000000010d941760: 4800 0058 | 0001 1fd6 | 80c7 cc14 | 0100 0000 

  0x000000010d941770: ;   {static_stub}
  0x000000010d941770: df3f 03d5 

  0x000000010d941774: ;   {metadata(nullptr)}
  0x000000010d941774: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000010d941790: ;   {static_stub}
  0x000000010d941790: df3f 03d5 

  0x000000010d941794: ;   {metadata(nullptr)}
  0x000000010d941794: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 
[Exception Handler]
  0x000000010d9417b0: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000000010d9417b0: f4a8 d195 | c1d5 bbd4 | a155 a004 | 0100 0000 
[Deopt Handler Code]
  0x000000010d9417c0: 1e00 0010 

  0x000000010d9417c4: ;   {runtime_call DeoptimizationBlob}
  0x000000010d9417c4: 1f45 ce15 
[/MachCode]


Compiled method (c1) 242905 1127       3       org.example.sbe.MessageHeaderDecoder::sequence (20 bytes)
 total in heap  [0x000000010d942090,0x000000010d942580] = 1264
 relocation     [0x000000010d9421e0,0x000000010d942220] = 64
 main code      [0x000000010d942240,0x000000010d942450] = 528
 stub code      [0x000000010d942450,0x000000010d942498] = 72
 oops           [0x000000010d942498,0x000000010d9424a8] = 16
 metadata       [0x000000010d9424a8,0x000000010d9424e0] = 56
 scopes data    [0x000000010d9424e0,0x000000010d942500] = 32
 scopes pcs     [0x000000010d942500,0x000000010d942560] = 96
 dependencies   [0x000000010d942560,0x000000010d942570] = 16
 nul chk table  [0x000000010d942570,0x000000010d942580] = 16

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000001234465b0} 'sequence' '()J' in 'org/example/sbe/MessageHeaderDecoder'
  #           [sp+0x40]  (sp of caller)
  0x000000010d942240: 2808 40b9 | 3f01 086b | c001 0054 

  0x000000010d94224c: ;   {runtime_call ic_miss_stub}
  0x000000010d94224c: 0d27 ce15 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 
  0x000000010d94226c: 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 
[Verified Entry Point]
  0x000000010d942280: 1f20 03d5 | e953 40d1 | 3f01 00f9 | ff03 01d1 | fd7b 03a9 | 2801 0018 | 8923 40b9 | 1f01 09eb 
  0x000000010d9422a0: e000 0054 | 0830 8bd2 | a899 a2f2 | 2800 c0f2 | 0001 3fd6 | 0200 0014 | 0100 0000 

  0x000000010d9422bc: ;   {metadata(method data for {method} {0x00000001234465b0} 'sequence' '()J' in 'org/example/sbe/MessageHeaderDecoder')}
  0x000000010d9422bc: 029b 89d2 | 2270 a4f2 | 2200 c0f2 | 43ac 40b9 | 6308 0011 | 43ac 00b9 | 6324 1f12 | 7f00 0071 
  0x000000010d9422dc: 2009 0054 | 2010 40b9 | 00f0 7dd3 | 220c 40b9 | 4001 00b4 

  0x000000010d9422f0: ;   {metadata('org/agrona/AbstractMutableDirectBuffer')}
  0x000000010d9422f0: 059a 92d2 | 0520 a0f2 | 0514 c0f2 | 0608 40b9 | 0614 c0f2 | c81c 40f9 | bf00 08eb | a108 0054 
  0x000000010d942310: 0100 0014 | e303 00aa 

  0x000000010d942318: ; implicit exception: dispatches to 0x000000010d942424
  0x000000010d942318: 1f00 40f9 | e303 00aa 

  0x000000010d942320: ;   {metadata(method data for {method} {0x00000001234465b0} 'sequence' '()J' in 'org/example/sbe/MessageHeaderDecoder')}
  0x000000010d942320: 049b 89d2 | 2470 a4f2 | 2400 c0f2 | 6308 40b9 | 0314 c0f2 | 8900 0491 | 2801 40f9 | 7f00 08eb 
  0x000000010d942340: a100 0054 | 8884 40f9 | 0805 0091 | 8884 00f9 | 1c00 0014 | 8940 0491 | 2801 40f9 | 7f00 08eb 
  0x000000010d942360: a100 0054 | 888c 40f9 | 0805 0091 | 888c 00f9 | 1400 0014 | 8900 0491 | 2801 40f9 | c800 00b5 
  0x000000010d942380: 2301 00f9 | e803 40b2 | 8920 0491 | 2801 00f9 | 0c00 0014 | 8940 0491 | 2801 40f9 | c800 00b5 
  0x000000010d9423a0: 2301 00f9 | e803 40b2 | 8960 0491 | 2801 00f9 | 0400 0014 | 8878 40f9 | 0805 0091 | 8878 00f9 
  0x000000010d9423c0: 4220 0011 

  0x000000010d9423c4: ;   {oop(a 'java/nio/ByteOrder'{0x000000070e89bb88})}
  0x000000010d9423c4: 0371 97d2 | 23d1 a1f2 | e300 c0f2 | e113 00f9 | e103 00aa 

  0x000000010d9423d8: ; ImmutableOopMap {[32]=Oop }
                      ;*invokeinterface getLong {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.example.sbe.MessageHeaderDecoder::sequence@14 (line 234)
                      ;   {optimized virtual_call}
  0x000000010d9423d8: 3afc ff97 

  0x000000010d9423dc: ;   {other}
  0x000000010d9423dc: 1f20 03d5 | 9f69 80f2 | 1f00 80f2 | fd7b 43a9 | ff03 0191 

  0x000000010d9423f0: ;   {poll_return}
  0x000000010d9423f0: 882b 42f9 | ff63 28eb | 8801 0054 | c003 5fd6 

  0x000000010d942400: ;   {metadata({method} {0x00000001234465b0} 'sequence' '()J' in 'org/example/sbe/MessageHeaderDecoder')}
  0x000000010d942400: 08b6 8cd2 | 8868 a4f2 | 2800 c0f2 | e807 00f9 | 0800 8092 | e803 00f9 

  0x000000010d942418: ; ImmutableOopMap {c_rarg1=Oop }
                      ;*synchronization entry
                      ; - org.example.sbe.MessageHeaderDecoder::sequence@-1 (line 234)
                      ;   {runtime_call counter_overflow Runtime1 stub}
  0x000000010d942418: 9ab4 d195 | b1ff ff17 

  0x000000010d942420: ; ImmutableOopMap {c_rarg1=Oop c_rarg0=Oop }
                      ;*invokeinterface getLong {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.example.sbe.MessageHeaderDecoder::sequence@14 (line 234)
                      ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000000010d942420: 18a8 d195 

  0x000000010d942424: ; ImmutableOopMap {c_rarg1=Oop c_rarg0=Oop }
                      ;*invokeinterface getLong {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.example.sbe.MessageHeaderDecoder::sequence@14 (line 234)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000000010d942424: 579e d195 

  0x000000010d942428: ;   {internal_word}
  0x000000010d942428: 48fe ff10 | 8837 02f9 

  0x000000010d942430: ;   {runtime_call SafepointBlob}
  0x000000010d942430: 3441 ce15 | 8083 42f9 | 9f83 02f9 | 9f87 02f9 | fd7b 43a9 | ff03 0191 

  0x000000010d942448: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000000010d942448: 8e9a d115 | 0000 0000 
[Stub Code]
  0x000000010d942450: ;   {no_reloc}
  0x000000010d942450: df3f 03d5 

  0x000000010d942454: ;   {metadata(nullptr)}
  0x000000010d942454: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000010d942470: ;   {trampoline_stub}
  0x000000010d942470: 4800 0058 | 0001 1fd6 | c014 940d | 0100 0000 
[Exception Handler]
  0x000000010d942480: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000000010d942480: c0a5 d195 | c1d5 bbd4 | a155 a004 | 0100 0000 
[Deopt Handler Code]
  0x000000010d942490: 1e00 0010 

  0x000000010d942494: ;   {runtime_call DeoptimizationBlob}
  0x000000010d942494: eb41 ce15 
[/MachCode]


Compiled method (c2) 242907 1210 %     4       org.agrona.concurrent.AgentRunner::workLoop @ 0 (17 bytes)
 total in heap  [0x0000000115298110,0x00000001152989d0] = 2240
 relocation     [0x0000000115298260,0x00000001152982f8] = 152
 main code      [0x0000000115298300,0x00000001152986a0] = 928
 stub code      [0x00000001152986a0,0x0000000115298740] = 160
 oops           [0x0000000115298740,0x0000000115298748] = 8
 metadata       [0x0000000115298748,0x00000001152987a8] = 96
 scopes data    [0x00000001152987a8,0x0000000115298860] = 184
 scopes pcs     [0x0000000115298860,0x0000000115298930] = 208
 dependencies   [0x0000000115298930,0x0000000115298948] = 24
 handler table  [0x0000000115298948,0x00000001152989c0] = 120
 nul chk table  [0x00000001152989c0,0x00000001152989d0] = 16

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x0000000123407ac0} 'workLoop' '(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V' in 'org/agrona/concurrent/AgentRunner'
  0x0000000115298300: 0000 20d4 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | e953 40d1 | 3f01 00f9 | ffc3 01d1 
  0x0000000115298320: fd7b 06a9 | a81b 0018 | 8923 40b9 | 1f01 09eb | a11a 0054 | 8ac7 41f9 | 34f4 40a9 | 3300 40f9 
  0x0000000115298340: 5501 40f9 | e003 01aa | c900 0010 

  0x000000011529834c: ;   {runtime_call SharedRuntime::OSR_migration_end(long*)}
  0x000000011529834c: 08a0 85d2 | 888e a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x0000000115298360: ;   {other}
  0x0000000115298360: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 

  0x0000000115298370: ; implicit exception: dispatches to 0x0000000115298658
  0x0000000115298370: ab0b 40b9 

  0x0000000115298374: ;   {metadata('org/agrona/concurrent/AgentRunner')}
  0x0000000115298374: 0a20 a0d2 | 0a40 86f2 | 7f01 0a6b | 4115 0054 | aa33 0091 | adee 0091 | ac33 0091 | 2200 0014 
  0x0000000115298394: 0a08 40b9 

  0x0000000115298398: ;   {metadata('java/lang/InterruptedException')}
  0x0000000115298398: 8b20 a0d2 | 0b13 95f2 | 5f01 0b6b | 4108 0054 | fd03 00aa | 0700 0014 | 0b08 40b9 

  0x00000001152983b4: ;   {metadata('java/lang/InterruptedException')}
  0x00000001152983b4: 8a20 a0d2 | 0a13 95f2 | 7f01 0a6b | 2108 0054 | fd03 00aa | ea1b 40f9 | 5ffd 9f08 | e10b 40f9 
  0x00000001152983d4: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop [40]=Derived_oop_[16] [24]=Oop [48]=Derived_oop_[24] [32]=Derived_oop_[24] }
                      ;*invokevirtual interrupt {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@44 (line 314)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x00000001152983d4: 6bcf e897 

  0x00000001152983d8: ;   {other}
  0x00000001152983d8: 1f20 03d5 | 1f59 80f2 | 1f00 80f2 | f577 41a9 | ed17 40f9 | 0600 0014 | ed17 40f9 | 1f20 03d5 
  0x00000001152983f8: 1f20 03d5 | 1f20 03d5 | f577 41a9 

  0x0000000115298404: ; ImmutableOopMap {rdispatch=Oop r13=Derived_oop_rdispatch rfp=Oop [48]=Derived_oop_rfp [32]=Derived_oop_rfp [0]=Oop [8]=Oop }
                      ;*goto {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::workLoop@13 (line 296)
  0x0000000115298404: 8a2f 42f9 

  0x0000000115298408: ;   {poll}
  0x0000000115298408: 5f01 40b9 | f44f 40a9 | ea13 40f9 | ec1b 40f9 | 4bfd df08 | 0b06 0034 | f44f 00a9 | f577 01a9 
  0x0000000115298428: ea37 02a9 | ec1b 00f9 | e103 13aa | 0980 89d2 | 0910 a0f2 | 0900 ccf2 

  0x0000000115298440: ; ImmutableOopMap {rfp=Oop [48]=Derived_oop_rfp [32]=Derived_oop_rfp [0]=Oop [8]=Oop [16]=Oop [40]=Derived_oop_[16] [24]=Oop }
                      ;*invokeinterface doWork {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@1 (line 304)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {virtual_call}
  0x0000000115298440: 028a ec97 

  0x0000000115298444: ;   {other}
  0x0000000115298444: 1f20 03d5 | 9f66 80f2 | 1f40 80f2 | fd03 002a | e103 40f9 | e203 002a | 0904 8ad2 | 0910 a0f2 
  0x0000000115298464: 0900 ccf2 

  0x0000000115298468: ; ImmutableOopMap {[0]=Oop [8]=Oop [16]=Oop [40]=Derived_oop_[16] [24]=Oop [48]=Derived_oop_[24] [32]=Derived_oop_[24] }
                      ;*invokeinterface idle {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@9 (line 305)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {virtual_call}
  0x0000000115298468: 248a ec97 

  0x000000011529846c: ;   {other}
  0x000000011529846c: 1f20 03d5 | 9f6b 80f2 | 1f60 80f2 | ea03 1d2a | 5f01 0071 | 8cfb ff54 | ed17 40f9 | acfd df08 
  0x000000011529848c: acfb ff34 | 4117 8012 | fd0f 40f9 | ea33 0529 

  0x000000011529849c: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::doWork@24 (line 306)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000011529849c: b9ea e897 

  0x00000001152984a0: ;   {other}
  0x00000001152984a0: 1f20 03d5 | 1f72 80f2 | 1f80 80f2 

  0x00000001152984ac: ;   {metadata('java/nio/channels/ClosedByInterruptException')}
  0x00000001152984ac: cc20 a0d2 | 0cf6 8bf2 | 5f01 0c6b | 0102 0054 | fd03 00aa | c2ff ff17 

  0x00000001152984c4: ;   {metadata('java/nio/channels/ClosedByInterruptException')}
  0x00000001152984c4: cc20 a0d2 | 0cf6 8bf2 | 7f01 0c6b | 0102 0054 | fd03 00aa | bcff ff17 | 4117 8012 | f44f 00a9 
  0x00000001152984e4: eb13 00b9 

  0x00000001152984e8: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::workLoop@4 (line 294)
                      ;   {runtime_call UncommonTrapBlob}
  0x00000001152984e8: a6ea e897 

  0x00000001152984ec: ;   {other}
  0x00000001152984ec: 1f20 03d5 | 9f7b 80f2 | 1fa0 80f2 

  0x00000001152984f8: ;   {metadata('org/agrona/concurrent/AgentTerminationException')}
  0x00000001152984f8: 0c20 a0d2 | 0c8b 86f2 | 5f01 0c6b | 8102 0054 | fd03 00aa | 0600 0014 

  0x0000000115298510: ;   {metadata('org/agrona/concurrent/AgentTerminationException')}
  0x0000000115298510: 0a20 a0d2 | 0a8b 86f2 | 7f01 0a6b | 0102 0054 | fd03 00aa | ea0f 40f9 | 4a31 0091 | 5ffd 9f08 
  0x0000000115298530: e10f 40f9 | e203 1daa 

  0x0000000115298538: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop [40]=Derived_oop_[16] [24]=Oop [48]=Derived_oop_[24] [32]=Derived_oop_[24] }
                      ;*invokespecial handleError {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@58 (line 319)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x0000000115298538: 12cf e897 

  0x000000011529853c: ;   {other}
  0x000000011529853c: 1f20 03d5 | 9f85 80f2 | 1fc0 80f2 | f577 41a9 | ed17 40f9 | adff ff17 | e01f 00f9 | 0200 0014 
  0x000000011529855c: e01f 00f9 | ec0b 40f9 | 8aed 0091 | 4bfd df08 | 2b05 0034 | eb0f 40f9 | 6a31 0091 | 5ffd 9f08 
  0x000000011529857c: ea1f 40f9 | 4a09 40b9 | 0a14 c0f2 | 4a21 40f9 | 7d31 0091 | eb0f 00f9 | ea23 00f9 | e103 0baa 
  0x000000011529859c: e21f 40f9 

  0x00000001152985a0: ; ImmutableOopMap {[0]=Oop [8]=Oop [16]=Oop [40]=Derived_oop_[16] [24]=Oop [48]=Derived_oop_[24] [32]=Derived_oop_[24] rfp=Derived_oop_[24] [56]=Oop }
                      ;*invokespecial handleError {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::doWork@81 (line 328)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {optimized virtual_call}
  0x00000001152985a0: f8ce e897 

  0x00000001152985a4: ;   {other}
  0x00000001152985a4: 1f20 03d5 | 9f92 80f2 | 1fe0 80f2 | abff df08 | 2b03 0034 | f50b 40f9 | aaee 0091 | 4afd df08 
  0x00000001152985c4: ea02 0034 | fd0f 40f9 | aa33 0091 | 5ffd 9f08 

  0x00000001152985d4: ;   {metadata('java/lang/Error')}
  0x00000001152985d4: 0a3c 83d2 | 2a00 a0f2 | 0a14 c0f2 | eb23 40f9 | 7f01 0aeb | 0101 0054 | eb1f 40f9 | 8ac7 45b9 
  0x00000001152985f4: 6a02 0035 | e103 0baa | fd7b 46a9 | ffc3 0191 

  0x0000000115298604: ;   {runtime_call _rethrow_Java}
  0x0000000115298604: 1f76 ec17 | ed17 40f9 | 7eff ff17 | eb0f 40f9 | daff ff17 | f577 41a9 | eeff ff17 | fd0f 40f9 
  0x0000000115298624: ecff ff17 | 410e 8012 | f44f 00a9 

  0x0000000115298630: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::workLoop@0 (line 294)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000115298630: 54ea e897 

  0x0000000115298634: ;   {other}
  0x0000000115298634: 1f20 03d5 | 9fa4 80f2 | 1f00 81f2 | eb17 00f9 | 010d 8012 

  0x0000000115298648: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [40]=Oop [56]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.agrona.concurrent.AgentRunner::doWork@116 (line 336)
                      ; - org.agrona.concurrent.AgentRunner::workLoop@10 (line 296)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000115298648: 4eea e897 

  0x000000011529864c: ;   {other}
  0x000000011529864c: 1f20 03d5 | 9fa7 80f2 | 1f20 81f2 | 2111 8012 | fd03 14aa | f303 00f9 

  0x0000000115298664: ; ImmutableOopMap {rfp=Oop [0]=Oop }
                      ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.agrona.concurrent.AgentRunner::workLoop@0 (line 294)
                      ;   {runtime_call UncommonTrapBlob}
  0x0000000115298664: 47ea e897 

  0x0000000115298668: ;   {other}
  0x0000000115298668: 1f20 03d5 | 1fab 80f2 | 1f40 81f2 | 0200 0014 | 0100 0014 | eb03 00aa | deff ff17 | 0830 8bd2 
  0x0000000115298688: a899 a2f2 | 2800 c0f2 | 0001 3fd6 | 28ff ff17 

  0x0000000115298698: ;   {other}
  0x0000000115298698: 0100 0000 | 0000 0000 
[Stub Code]
  0x00000001152986a0: ;   {no_reloc}
  0x00000001152986a0: 4800 0058 | 0001 1fd6 | 80c1 cc14 | 0100 0000 

  0x00000001152986b0: ;   {static_stub}
  0x00000001152986b0: df3f 03d5 

  0x00000001152986b4: ;   {metadata(nullptr)}
  0x00000001152986b4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x00000001152986d0: ;   {trampoline_stub}
  0x00000001152986d0: 4800 0058 | 0001 1fd6 | 48ac db14 | 0100 0000 

  0x00000001152986e0: ;   {trampoline_stub}
  0x00000001152986e0: 4800 0058 | 0001 1fd6 | f8ac db14 | 0100 0000 

  0x00000001152986f0: ;   {trampoline_stub}
  0x00000001152986f0: 4800 0058 | 0001 1fd6 | 80c1 cc14 | 0100 0000 

  0x0000000115298700: ;   {trampoline_stub}
  0x0000000115298700: 4800 0058 | 0001 1fd6 | 80c1 cc14 | 0100 0000 

  0x0000000115298710: ;   {static_stub}
  0x0000000115298710: df3f 03d5 

  0x0000000115298714: ;   {metadata(nullptr)}
  0x0000000115298714: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 
[Exception Handler]
  0x0000000115298730: ;   {runtime_call ExceptionBlob}
  0x0000000115298730: b465 ec17 
[Deopt Handler Code]
  0x0000000115298734: 1e00 0010 

  0x0000000115298738: ;   {runtime_call DeoptimizationBlob}
  0x0000000115298738: 42e9 e817 | 0000 0000 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000060000080d880, length=20, elements={
0x000000014483da00, 0x0000000144842200, 0x000000014483e200, 0x000000014483ea00,
0x000000014483f200, 0x000000014308e400, 0x000000014484ce00, 0x000000014308f800,
0x000000014280a600, 0x0000000142885200, 0x0000000142885a00, 0x0000000142886200,
0x000000014484e600, 0x0000000144874c00, 0x0000000144879000, 0x000000012a808200,
0x000000012b008e00, 0x00000001438ec400, 0x000000014396d800, 0x0000000143951200
}

Java Threads: ( => current thread )
  0x000000014483da00 JavaThread "Reference Handler"          daemon [_thread_blocked, id=30211, stack(0x000000016e6f4000,0x000000016e8f7000) (2060K)]
  0x0000000144842200 JavaThread "Finalizer"                  daemon [_thread_blocked, id=25091, stack(0x000000016e900000,0x000000016eb03000) (2060K)]
  0x000000014483e200 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=29699, stack(0x000000016eb0c000,0x000000016ed0f000) (2060K)]
  0x000000014483ea00 JavaThread "Service Thread"             daemon [_thread_blocked, id=25603, stack(0x000000016ed18000,0x000000016ef1b000) (2060K)]
  0x000000014483f200 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=25859, stack(0x000000016ef24000,0x000000016f127000) (2060K)]
  0x000000014308e400 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=26371, stack(0x000000016f130000,0x000000016f333000) (2060K)]
  0x000000014484ce00 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=29187, stack(0x000000016f33c000,0x000000016f53f000) (2060K)]
  0x000000014308f800 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=26883, stack(0x000000016f548000,0x000000016f74b000) (2060K)]
  0x000000014280a600 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=27139, stack(0x000000016f754000,0x000000016f957000) (2060K)]
  0x0000000142885200 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=28163, stack(0x000000016f960000,0x000000016fb63000) (2060K)]
  0x0000000142885a00 JavaThread "JDWP Command Reader"        daemon [_thread_in_native, id=27651, stack(0x000000016fb6c000,0x000000016fd6f000) (2060K)]
  0x0000000142886200 JavaThread "IntelliJ Suspend Helper"    daemon [_thread_blocked, id=32771, stack(0x000000016fd78000,0x000000016ff7b000) (2060K)]
  0x000000014484e600 JavaThread "Notification Thread"        daemon [_thread_blocked, id=43011, stack(0x0000000300004000,0x0000000300207000) (2060K)]
  0x0000000144874c00 JavaThread ""                                  [_thread_blocked, id=33283, stack(0x0000000300210000,0x0000000300413000) (2060K)]
  0x0000000144879000 JavaThread ""                                  [_thread_blocked, id=42755, stack(0x000000030041c000,0x000000030061f000) (2060K)]
=>0x000000012a808200 JavaThread ""                                  [_thread_in_Java, id=42499, stack(0x0000000300628000,0x000000030082b000) (2060K)]
  0x000000012b008e00 JavaThread "DestroyJavaVM"                     [_thread_blocked, id=4867, stack(0x000000016d57c000,0x000000016d77f000) (2060K)]
  0x00000001438ec400 JavaThread "driver-conductor"                  [_thread_blocked, id=41987, stack(0x0000000300834000,0x0000000300a37000) (2060K)]
  0x000000014396d800 JavaThread "sender"                            [_thread_blocked, id=34307, stack(0x0000000300a40000,0x0000000300c43000) (2060K)]
  0x0000000143951200 JavaThread "receiver"                          [_thread_blocked, id=41731, stack(0x0000000300c4c000,0x0000000300e4f000) (2060K)]
Total: 20

Other Threads:
  0x000000014270ced0 VMThread "VM Thread"                           [id=20227, stack(0x000000016e3d0000,0x000000016e5d3000) (2060K)]
  0x00000001441088a0 WatcherThread "VM Periodic Task Thread"        [id=20995, stack(0x000000016e1c4000,0x000000016e3c7000) (2060K)]
  0x0000000144106880 WorkerThread "GC Thread#0"                     [id=14083, stack(0x000000016d788000,0x000000016d98b000) (2060K)]
  0x0000000144123b10 WorkerThread "GC Thread#1"                     [id=36099, stack(0x0000000301894000,0x0000000301a97000) (2060K)]
  0x0000000144124080 WorkerThread "GC Thread#2"                     [id=39683, stack(0x0000000301aa0000,0x0000000301ca3000) (2060K)]
  0x00000001441245f0 WorkerThread "GC Thread#3"                     [id=39171, stack(0x0000000301cac000,0x0000000301eaf000) (2060K)]
  0x0000000144124b60 WorkerThread "GC Thread#4"                     [id=38659, stack(0x0000000301eb8000,0x00000003020bb000) (2060K)]
  0x00000001441250d0 WorkerThread "GC Thread#5"                     [id=38147, stack(0x00000003020c4000,0x00000003022c7000) (2060K)]
  0x0000000144125640 WorkerThread "GC Thread#6"                     [id=36867, stack(0x00000003022d0000,0x00000003024d3000) (2060K)]
  0x00000001426219c0 WorkerThread "GC Thread#7"                     [id=37379, stack(0x00000003024dc000,0x00000003026df000) (2060K)]
  0x0000000142707cd0 ConcurrentGCThread "G1 Main Marker"            [id=13827, stack(0x000000016d994000,0x000000016db97000) (2060K)]
  0x0000000142708650 WorkerThread "G1 Conc#0"                       [id=12547, stack(0x000000016dba0000,0x000000016dda3000) (2060K)]
  0x0000000143087000 ConcurrentGCThread "G1 Refine#0"               [id=12803, stack(0x000000016ddac000,0x000000016dfaf000) (2060K)]
  0x0000000144106df0 ConcurrentGCThread "G1 Service"                [id=16643, stack(0x000000016dfb8000,0x000000016e1bb000) (2060K)]
Total: 14

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000000a000000000-0x000000a000cf0000-0x000000a000cf0000), size 13565952, SharedBaseAddress: 0x000000a000000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000000a001000000-0x000000a041000000, reserved size: 1073741824
Narrow klass base: 0x000000a000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 8 total, 8 available
 Memory: 16384M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 264192K, used 9000K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 1 survivors (2048K)
 Metaspace       used 9268K, committed 9536K, reserved 1114112K
  class space    used 768K, committed 896K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000700000000, 0x0000000700000000, 0x0000000700200000|  0%| F|  |TAMS 0x0000000700000000| PB 0x0000000700000000| Untracked 
|   1|0x0000000700200000, 0x0000000700200000, 0x0000000700400000|  0%| F|  |TAMS 0x0000000700200000| PB 0x0000000700200000| Untracked 
|   2|0x0000000700400000, 0x0000000700400000, 0x0000000700600000|  0%| F|  |TAMS 0x0000000700400000| PB 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700600000, 0x0000000700800000|  0%| F|  |TAMS 0x0000000700600000| PB 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700800000, 0x0000000700a00000|  0%| F|  |TAMS 0x0000000700800000| PB 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700a00000, 0x0000000700c00000|  0%| F|  |TAMS 0x0000000700a00000| PB 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700c00000, 0x0000000700e00000|  0%| F|  |TAMS 0x0000000700c00000| PB 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000700e00000, 0x0000000701000000|  0%| F|  |TAMS 0x0000000700e00000| PB 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701000000, 0x0000000701200000|  0%| F|  |TAMS 0x0000000701000000| PB 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701200000, 0x0000000701400000|  0%| F|  |TAMS 0x0000000701200000| PB 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701400000, 0x0000000701600000|  0%| F|  |TAMS 0x0000000701400000| PB 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701600000, 0x0000000701800000|  0%| F|  |TAMS 0x0000000701600000| PB 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701800000, 0x0000000701a00000|  0%| F|  |TAMS 0x0000000701800000| PB 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701a00000, 0x0000000701c00000|  0%| F|  |TAMS 0x0000000701a00000| PB 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701c00000, 0x0000000701e00000|  0%| F|  |TAMS 0x0000000701c00000| PB 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000701e00000, 0x0000000702000000|  0%| F|  |TAMS 0x0000000701e00000| PB 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702000000, 0x0000000702200000|  0%| F|  |TAMS 0x0000000702000000| PB 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702200000, 0x0000000702400000|  0%| F|  |TAMS 0x0000000702200000| PB 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702400000, 0x0000000702600000|  0%| F|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702600000, 0x0000000702800000|  0%| F|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|  25|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  49|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  50|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  56|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  58|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  59|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  64|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  67|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  86|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
| 116|0x000000070e800000, 0x000000070e9bf3a8, 0x000000070ea00000| 87%| S|CS|TAMS 0x000000070e800000| PB 0x000000070e800000| Complete 
| 117|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
| 118|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
| 119|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 120|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 121|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 122|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 123|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 124|0x000000070f800000, 0x000000070f9667f8, 0x000000070fa00000| 70%| E|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Complete 
| 125|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%| E|CS|TAMS 0x000000070fa00000| PB 0x000000070fa00000| Complete 
| 126|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| E|CS|TAMS 0x000000070fc00000| PB 0x000000070fc00000| Complete 
| 127|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| E|CS|TAMS 0x000000070fe00000| PB 0x000000070fe00000| Complete 
|2047|0x00000007ffe00000, 0x00000007fff0af50, 0x0000000800000000| 52%| O|  |TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Untracked 

Card table byte_map: [0x0000000103604000,0x0000000103e04000] _byte_map_base: 0x00000000ffe04000

Marking Bits: (CMBitMap*) 0x0000000143010810
 Bits: [0x000000011c75c000, 0x000000012075c000)

Polling page: 0x00000001029a4000

Metaspace:

Usage:
  Non-class:      8.30 MB used.
      Class:    768.95 KB used.
       Both:      9.05 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       8.44 MB ( 13%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     896.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       9.31 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  6.73 MB
       Class:  15.04 MB
        Both:  21.77 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 158.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 149.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 355.
num_chunk_merges: 0.
num_chunk_splits: 231.
num_chunks_enlarged: 168.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120032Kb used=650Kb max_used=650Kb free=119381Kb
 bounds [0x0000000115224000, 0x0000000115494000, 0x000000011c75c000]
CodeHeap 'profiled nmethods': size=120016Kb used=2491Kb max_used=2491Kb free=117524Kb
 bounds [0x000000010d75c000, 0x000000010d9cc000, 0x0000000114c90000]
CodeHeap 'non-nmethods': size=5712Kb used=1416Kb max_used=1459Kb free=4295Kb
 bounds [0x0000000114c90000, 0x0000000114f00000, 0x0000000115224000]
 total_blobs=1897 nmethods=1353 adapters=453
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 241.173 Thread 0x000000014308e400 1353       4       io.aeron.Publication::offer (9 bytes)
Event: 241.175 Thread 0x000000014308e400 nmethod 1353 0x00000001152c1710 code [0x00000001152c1900, 0x00000001152c1dd8]
Event: 241.175 Thread 0x000000014308e400 1345       4       org.example.services.PositionNodeApp::publishOrderEvent (99 bytes)
Event: 241.184 Thread 0x000000014308e400 nmethod 1345 0x00000001152c2490 code [0x00000001152c2700, 0x00000001152c32d0]
Event: 241.184 Thread 0x000000014308e400 1347       4       org.example.sbe.OrderEncoder::wrapAndApplyHeader (34 bytes)
Event: 241.186 Thread 0x000000014308e400 nmethod 1347 0x00000001152c4190 code [0x00000001152c4340, 0x00000001152c46e8]
Event: 241.186 Thread 0x000000014308e400 1348       4       org.example.sbe.OrderEncoder::orderType (24 bytes)
Event: 241.187 Thread 0x000000014308e400 nmethod 1348 0x00000001152c4c10 code [0x00000001152c4dc0, 0x00000001152c4f80]
Event: 241.187 Thread 0x000000014308e400 1349       4       org.example.sbe.OrderEncoder::quantity (23 bytes)
Event: 241.187 Thread 0x000000014308e400 nmethod 1349 0x00000001152c5190 code [0x00000001152c5340, 0x00000001152c54e8]
Event: 241.187 Thread 0x000000014308e400 1350       4       org.example.sbe.OrderEncoder::encodedLength (10 bytes)
Event: 241.187 Thread 0x000000014308e400 nmethod 1350 0x00000001152c5710 code [0x00000001152c5880, 0x00000001152c5900]
Event: 241.431 Thread 0x000000014308e400 1359       4       java.util.ArrayDeque::pollFirst (36 bytes)
Event: 241.432 Thread 0x000000014308e400 nmethod 1359 0x00000001152c5a10 code [0x00000001152c5b80, 0x00000001152c5cf8]
Event: 241.460 Thread 0x000000014308e400 1360       4       org.agrona.concurrent.status.AtomicCounter::proposeMaxOrdered (44 bytes)
Event: 241.460 Thread 0x000000014308e400 nmethod 1360 0x00000001152c5e10 code [0x00000001152c5f80, 0x00000001152c6058]
Event: 241.541 Thread 0x000000014308e400 1361       4       org.agrona.concurrent.status.AtomicCounter::getAndAddOrdered (38 bytes)
Event: 241.542 Thread 0x000000014308e400 nmethod 1361 0x00000001152c6110 code [0x00000001152c6280, 0x00000001152c6348]
Event: 241.542 Thread 0x000000014308e400 1362       4       org.agrona.concurrent.OneToOneConcurrentArrayQueue::drain (114 bytes)
Event: 241.543 Thread 0x000000014308e400 nmethod 1362 0x00000001152c6410 code [0x00000001152c65c0, 0x00000001152c6840]

GC Heap History (2 events):
Event: 0.403 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 264192K, used 23595K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 0 survivors (0K)
 Metaspace       used 8856K, committed 9088K, reserved 1114112K
  class space    used 745K, committed 896K, reserved 1048576K
}
Event: 0.404 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 264192K, used 2856K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 8856K, committed 9088K, reserved 1114112K
  class space    used 745K, committed 896K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.004 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjava.dylib
Event: 0.005 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libzip.dylib
Event: 0.040 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libinstrument.dylib
Event: 0.060 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnio.dylib
Event: 0.065 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libzip.dylib
Event: 0.092 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnet.dylib
Event: 0.107 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjimage.dylib
Event: 0.116 Loaded shared library /Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libextnet.dylib

Deoptimization events (20 events):
Event: 241.156 Thread 0x0000000142a17800 DEOPT PACKING pc=0x000000010d91acc0 sp=0x00000003014727e0
Event: 241.156 Thread 0x0000000142a17800 DEOPT UNPACKING pc=0x0000000114cd2e7c sp=0x0000000301472490 mode 1
Event: 241.156 Thread 0x0000000142a17800 DEOPT PACKING pc=0x000000011526d7dc sp=0x0000000301472820
Event: 241.156 Thread 0x0000000142a17800 DEOPT UNPACKING pc=0x0000000114cd2e7c sp=0x00000003014724e0 mode 1
Event: 241.159 Thread 0x00000001439e7400 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000001152984ec relative=0x00000000000001ec
Event: 241.159 Thread 0x00000001439e7400 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000001152984ec method=org.agrona.concurrent.AgentRunner.workLoop(Lorg/agrona/concurrent/IdleStrategy;Lorg/agrona/concurrent/Agent;)V @ 4 c2
Event: 241.159 Thread 0x00000001439e7400 DEOPT PACKING pc=0x00000001152984ec sp=0x000000030167e890
Event: 241.159 Thread 0x00000001439e7400 DEOPT UNPACKING pc=0x0000000114cd301c sp=0x000000030167e870 mode 2
Event: 241.159 Thread 0x00000001438ec400 Uncommon trap: trap_request=0xffffffde fr.pc=0x000000011528bce0 relative=0x00000000000000a0
Event: 241.159 Thread 0x00000001438ec400 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000011528bce0 method=org.agrona.AbstractMutableDirectBuffer.putLong(IJ)V @ 4 c2
Event: 241.159 Thread 0x00000001438ec400 DEOPT PACKING pc=0x000000011528bce0 sp=0x0000000300a36390
Event: 241.159 Thread 0x00000001438ec400 DEOPT UNPACKING pc=0x0000000114cd301c sp=0x0000000300a36340 mode 2
Event: 241.159 Thread 0x00000001438ec400 Uncommon trap: trap_request=0xffffffde fr.pc=0x000000011528bce0 relative=0x00000000000000a0
Event: 241.159 Thread 0x00000001438ec400 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000011528bce0 method=org.agrona.AbstractMutableDirectBuffer.putLong(IJ)V @ 4 c2
Event: 241.159 Thread 0x00000001438ec400 DEOPT PACKING pc=0x000000011528bce0 sp=0x0000000300a36390
Event: 241.159 Thread 0x00000001438ec400 DEOPT UNPACKING pc=0x0000000114cd301c sp=0x0000000300a36340 mode 2
Event: 241.175 Thread 0x000000014353ca00 DEOPT PACKING pc=0x000000011526d7dc sp=0x000000030105a830
Event: 241.175 Thread 0x000000014353ca00 DEOPT UNPACKING pc=0x0000000114cd2e7c sp=0x000000030105a4f0 mode 1
Event: 241.175 Thread 0x0000000143525800 DEOPT PACKING pc=0x000000011526d7dc sp=0x0000000301266830
Event: 241.175 Thread 0x0000000143525800 DEOPT UNPACKING pc=0x0000000114cd2e7c sp=0x00000003012664f0 mode 1

Classes loaded (20 events):
Event: 241.159 Loading class java/util/IdentityHashMap$EntrySet
Event: 241.160 Loading class java/util/IdentityHashMap$EntrySet done
Event: 241.160 Loading class java/util/IdentityHashMap$EntryIterator
Event: 241.160 Loading class java/util/IdentityHashMap$EntryIterator done
Event: 241.161 Loading class java/util/IdentityHashMap$EntryIterator$Entry
Event: 241.161 Loading class java/util/IdentityHashMap$EntryIterator$Entry done
Event: 241.161 Loading class io/aeron/driver/ReceiverProxy
Event: 241.161 Loading class io/aeron/driver/ReceiverProxy done
Event: 241.161 Loading class io/aeron/driver/media/ReceiveChannelEndpoint
Event: 241.161 Loading class io/aeron/driver/media/ReceiveChannelEndpoint done
Event: 241.161 Loading class io/aeron/driver/ReceiverProxy
Event: 241.161 Loading class io/aeron/driver/ReceiverProxy done
Event: 241.161 Loading class io/aeron/driver/media/ReceiveChannelEndpoint
Event: 241.161 Loading class io/aeron/driver/media/ReceiveChannelEndpoint done
Event: 241.163 Loading class org/agrona/collections/Int2ObjectHashMap$EntrySet
Event: 241.163 Loading class org/agrona/collections/Int2ObjectHashMap$EntrySet done
Event: 241.164 Loading class org/agrona/collections/Int2ObjectHashMap$EntryIterator
Event: 241.164 Loading class org/agrona/collections/Int2ObjectHashMap$EntryIterator done
Event: 241.164 Loading class org/agrona/collections/Int2ObjectHashMap$AbstractIterator
Event: 241.164 Loading class org/agrona/collections/Int2ObjectHashMap$AbstractIterator done

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.050 Thread 0x000000014270ced0 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 0.096 Thread 0x000000012a808200 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7d4fa8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000070f7d4fa8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.097 Thread 0x000000012a808200 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7dee88}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, int, long, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070f7dee88) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.097 Thread 0x0000000144874c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7b7108}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long, long, long, long, java.lang.Object)'> (0x000000070f7b7108) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.097 Thread 0x0000000144879000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f707080}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f707080) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.097 Thread 0x000000012a808200 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7e2f18}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f7e2f18) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.101 Thread 0x0000000144879000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f735b78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000070f735b78) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.102 Thread 0x0000000144879000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7413f0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070f7413f0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.103 Thread 0x000000012a808200 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f47fa40}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f47fa40) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.103 Thread 0x0000000144879000 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7557e0}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f7557e0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.160 Thread 0x000000014396d800 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f154c58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000070f154c58) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.160 Thread 0x0000000143951200 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f1c5578}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000070f1c5578) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.161 Thread 0x0000000143951200 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f1cda50}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x000000070f1cda50) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.161 Thread 0x000000014396d800 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f1599f0}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x000000070f1599f0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.165 Thread 0x0000000144874c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ee4cfe0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x000000070ee4cfe0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.166 Thread 0x0000000144874c00 Exception <a 'sun/nio/fs/UnixException'{0x000000070ee54548}> (0x000000070ee54548) 
thrown [src/hotspot/share/prims/jni.cpp, line 520]
Event: 0.167 Thread 0x00000001438ec400 Implicit null exception at 0x000000011525abbc to 0x000000011525b18c
Event: 0.196 Thread 0x0000000144874c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ed5def0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, java.lang.Object, java.lang.Object)'> (0x000000070ed5def0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.204 Thread 0x00000001438ec400 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ea32e70}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070ea32e70) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.205 Thread 0x00000001438ec400 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ea36b38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070ea36b38) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 0.205 Thread 0x00000001438ec400 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ea39d50}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070ea39d50) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 239.556 Executing VM operation: ChangeSingleStep
Event: 239.556 Executing VM operation: ChangeSingleStep done
Event: 239.557 Executing VM operation: ChangeSingleStep
Event: 239.557 Executing VM operation: ChangeSingleStep done
Event: 239.572 Executing VM operation: get/set locals
Event: 239.572 Executing VM operation: get/set locals done
Event: 239.580 Executing VM operation: get/set locals
Event: 239.580 Executing VM operation: get/set locals done
Event: 239.581 Executing VM operation: get/set locals
Event: 239.581 Executing VM operation: get/set locals done
Event: 240.329 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 240.329 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 240.329 Executing VM operation: RendezvousGCThreads
Event: 240.329 Executing VM operation: RendezvousGCThreads done
Event: 241.333 Executing VM operation: Cleanup
Event: 241.333 Executing VM operation: Cleanup done
Event: 242.888 Executing VM operation: ChangeBreakpoints
Event: 242.888 Executing VM operation: ChangeBreakpoints done
Event: 242.893 Executing VM operation: get/set locals
Event: 242.893 Executing VM operation: get/set locals done

Memory protections (20 events):
Event: 0.053 Protecting memory [0x000000016fd78000,0x000000016fd84000] with protection modes 0
Event: 0.054 Protecting memory [0x0000000300004000,0x0000000300010000] with protection modes 0
Event: 0.072 Protecting memory [0x0000000300210000,0x000000030021c000] with protection modes 0
Event: 0.086 Protecting memory [0x000000030041c000,0x0000000300428000] with protection modes 0
Event: 0.087 Protecting memory [0x0000000300628000,0x0000000300634000] with protection modes 0
Event: 0.087 Protecting memory [0x000000016d57c000,0x000000016d588000] with protection modes 3
Event: 0.087 Protecting memory [0x000000016d57c000,0x000000016d588000] with protection modes 0
Event: 0.160 Protecting memory [0x0000000300834000,0x0000000300840000] with protection modes 0
Event: 0.160 Protecting memory [0x0000000300a40000,0x0000000300a4c000] with protection modes 0
Event: 0.160 Protecting memory [0x0000000300c4c000,0x0000000300c58000] with protection modes 0
Event: 0.205 Protecting memory [0x0000000300e58000,0x0000000300e64000] with protection modes 0
Event: 0.205 Protecting memory [0x0000000301064000,0x0000000301070000] with protection modes 0
Event: 0.207 Protecting memory [0x0000000301270000,0x000000030127c000] with protection modes 0
Event: 0.207 Protecting memory [0x000000030147c000,0x0000000301488000] with protection modes 0
Event: 0.207 Protecting memory [0x0000000301688000,0x0000000301694000] with protection modes 0
Event: 241.159 Protecting memory [0x000000030147c000,0x0000000301488000] with protection modes 3
Event: 241.160 Protecting memory [0x0000000301688000,0x0000000301694000] with protection modes 3
Event: 241.178 Protecting memory [0x0000000301270000,0x000000030127c000] with protection modes 3
Event: 241.199 Protecting memory [0x0000000301064000,0x0000000301070000] with protection modes 3
Event: 241.199 Protecting memory [0x0000000300e58000,0x0000000300e64000] with protection modes 3

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.053 Thread 0x0000000144808200 Thread added: 0x0000000142886200
Event: 0.054 Thread 0x0000000144808200 Thread added: 0x000000014484e600
Event: 0.072 Thread 0x0000000144808200 Thread added: 0x0000000144874c00
Event: 0.086 Thread 0x0000000144808200 Thread added: 0x0000000144879000
Event: 0.087 Thread 0x0000000144808200 Thread added: 0x000000012a808200
Event: 0.087 Thread 0x0000000144808200 Thread exited: 0x0000000144808200
Event: 0.087 Thread 0x000000012b008e00 Thread added: 0x000000012b008e00
Event: 0.160 Thread 0x0000000144874c00 Thread added: 0x00000001438ec400
Event: 0.160 Thread 0x0000000144874c00 Thread added: 0x000000014396d800
Event: 0.160 Thread 0x0000000144874c00 Thread added: 0x0000000143951200
Event: 0.205 Thread 0x000000012a808200 Thread added: 0x000000014353ca00
Event: 0.205 Thread 0x0000000144879000 Thread added: 0x0000000143525800
Event: 0.207 Thread 0x0000000144874c00 Thread added: 0x0000000142a17800
Event: 0.207 Thread 0x0000000142a17800 Thread added: 0x00000001439e7400
Event: 0.207 Thread 0x0000000142a17800 Thread added: 0x0000000143a0f800
Event: 241.159 Thread 0x00000001439e7400 Thread exited: 0x00000001439e7400
Event: 241.160 Thread 0x0000000143a0f800 Thread exited: 0x0000000143a0f800
Event: 241.178 Thread 0x0000000142a17800 Thread exited: 0x0000000142a17800
Event: 241.199 Thread 0x0000000143525800 Thread exited: 0x0000000143525800
Event: 241.199 Thread 0x000000014353ca00 Thread exited: 0x000000014353ca00


Dynamic libraries:
0x0000000102950000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjli.dylib
0x00000001aa162000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000191929000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x0000000194c18000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000018efb1000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000019bb95000 	/usr/lib/libSystem.B.dylib
0x0000000192dbb000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000023becb000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00000001a3188000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x0000000199729000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000019e35f000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000019e6b6000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000026a93b000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001f48b1000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000026f9b4000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000026e9fc000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000018ec15000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000019d7c3000 	/usr/lib/libspindump.dylib
0x0000000192f6d000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000019af87000 	/usr/lib/libbsm.0.dylib
0x00000001971ac000 	/usr/lib/libapp_launch_measurement.dylib
0x0000000196555000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00000001971b0000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x0000000198d43000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x0000000199f67000 	/usr/lib/liblangid.dylib
0x000000019972f000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x0000000193993000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x0000000193eb9000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00000001a3867000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000019d606000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x0000000198d20000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x0000000196586000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000019badf000 	/usr/lib/libz.1.dylib
0x00000001a777a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x0000000199714000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000019118e000 	/usr/lib/libicucore.A.dylib
0x000000019f73d000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000019e667000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001ba6d1000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00000001938de000 	/usr/lib/libMobileGestalt.dylib
0x000000019940d000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x0000000196a8d000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x0000000190d83000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00000001a31c4000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x0000000196eb3000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000019064e000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x0000000196674000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000019dc2c000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00000001938dc000 	/usr/lib/libenergytrace.dylib
0x00000001aea77000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00000001917d9000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00000001a35bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000019713d000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001ee281000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00000001971fa000 	/usr/lib/libxml2.2.dylib
0x000000019ae6b000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000018d558000 	/usr/lib/libobjc.A.dylib
0x000000018d865000 	/usr/lib/libc++.1.dylib
0x00000001a353c000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00000001945e9000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000018d9c1000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x0000000199ae9000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000019042f000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001ef7c6000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001efd4a000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001efd4d000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000019976a000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001f5474000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001e1a90000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000019bb9a000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001cd3a4000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000019f0d2000 	/usr/lib/swift/libswiftCore.dylib
0x00000001b6d7a000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001b6dd4000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001b418e000 	/usr/lib/swift/libswiftDarwin.dylib
0x00000002765c9000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00000001a50b3000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001b6dd5000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001c392b000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001d2901000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001a7d20000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00000002765f6000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001c8d52000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001cd395000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001b6d8c000 	/usr/lib/swift/libswiftXPC.dylib
0x00000002766dc000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x00000002766df000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000027683e000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00000002768d1000 	/usr/lib/swift/libswift_errno.dylib
0x00000002768d3000 	/usr/lib/swift/libswift_math.dylib
0x00000002768d6000 	/usr/lib/swift/libswift_signal.dylib
0x00000002768d7000 	/usr/lib/swift/libswift_stdio.dylib
0x00000002768d8000 	/usr/lib/swift/libswift_time.dylib
0x00000001a7d24000 	/usr/lib/swift/libswiftos.dylib
0x00000001ba628000 	/usr/lib/swift/libswiftsimd.dylib
0x00000002768d9000 	/usr/lib/swift/libswiftsys_time.dylib
0x00000002768da000 	/usr/lib/swift/libswiftunistd.dylib
0x000000019bdc2000 	/usr/lib/libcompression.dylib
0x000000019e2bf000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000019d2a5000 	/usr/lib/libate.dylib
0x000000019bb8f000 	/usr/lib/system/libcache.dylib
0x000000019bb4a000 	/usr/lib/system/libcommonCrypto.dylib
0x000000019bb75000 	/usr/lib/system/libcompiler_rt.dylib
0x000000019bb6a000 	/usr/lib/system/libcopyfile.dylib
0x000000018d6b3000 	/usr/lib/system/libcorecrypto.dylib
0x000000018d799000 	/usr/lib/system/libdispatch.dylib
0x000000018d959000 	/usr/lib/system/libdyld.dylib
0x000000019bb85000 	/usr/lib/system/libkeymgr.dylib
0x000000019bb2d000 	/usr/lib/system/libmacho.dylib
0x000000019af60000 	/usr/lib/system/libquarantine.dylib
0x000000019bb82000 	/usr/lib/system/libremovefile.dylib
0x0000000193958000 	/usr/lib/system/libsystem_asl.dylib
0x000000018d648000 	/usr/lib/system/libsystem_blocks.dylib
0x000000018d7e3000 	/usr/lib/system/libsystem_c.dylib
0x000000019bb79000 	/usr/lib/system/libsystem_collections.dylib
0x0000000199f54000 	/usr/lib/system/libsystem_configuration.dylib
0x0000000198cef000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000019b670000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000019145a000 	/usr/lib/system/libsystem_darwin.dylib
0x0000000276a11000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000019bb86000 	/usr/lib/system/libsystem_dnssd.dylib
0x0000000276a15000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000018d7e0000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000018d991000 	/usr/lib/system/libsystem_info.dylib
0x000000019baee000 	/usr/lib/system/libsystem_m.dylib
0x000000018d752000 	/usr/lib/system/libsystem_malloc.dylib
0x00000001938c1000 	/usr/lib/system/libsystem_networkextension.dylib
0x00000001918bc000 	/usr/lib/system/libsystem_notify.dylib
0x0000000199f59000 	/usr/lib/system/libsystem_sandbox.dylib
0x0000000276a1d000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000019bb7e000 	/usr/lib/system/libsystem_secinit.dylib
0x000000018d910000 	/usr/lib/system/libsystem_kernel.dylib
0x000000018d989000 	/usr/lib/system/libsystem_platform.dylib
0x000000018d94c000 	/usr/lib/system/libsystem_pthread.dylib
0x00000001954b2000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000018d697000 	/usr/lib/system/libsystem_trace.dylib
0x000000019bb58000 	/usr/lib/system/libunwind.dylib
0x000000018d64c000 	/usr/lib/system/libxpc.dylib
0x000000018d8f2000 	/usr/lib/libc++abi.dylib
0x00000002755e9000 	/usr/lib/libRosetta.dylib
0x0000000191758000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000019bb62000 	/usr/lib/liboah.dylib
0x000000019bb97000 	/usr/lib/libfakelink.dylib
0x00000001a722d000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001b3cb7000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00000001934f6000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x0000000197174000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x0000000191465000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00000001965e9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000019b677000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000019bce4000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000019542d000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000018df00000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000019d0fa000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x0000000197182000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000019bd79000 	/usr/lib/libapple_nghttp2.dylib
0x000000019508e000 	/usr/lib/libsqlite3.dylib
0x000000019fdb5000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00000001953c4000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00000001954bb000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000019bb31000 	/usr/lib/system/libkxld.dylib
0x0000000237a85000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000275441000 	/usr/lib/libCoreEntitlements.dylib
0x0000000252ec1000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x0000000195073000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000019b657000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000019af6f000 	/usr/lib/libcoretls.dylib
0x000000019d170000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000019bdbc000 	/usr/lib/libpam.2.dylib
0x000000019d1e4000 	/usr/lib/libxar.1.dylib
0x000000019bbec000 	/usr/lib/libarchive.2.dylib
0x00000001a14b7000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000023beef000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000025bee8000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000025cbba000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000027666d000 	/usr/lib/swift/libswiftSystem.dylib
0x0000000199f62000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001b6c65000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00000001a4574000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001f4776000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x0000000197385000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x0000000193424000 	/usr/lib/libboringssl.dylib
0x00000001954a1000 	/usr/lib/libdns_services.dylib
0x00000001b5e19000 	/usr/lib/libquic.dylib
0x000000019f061000 	/usr/lib/libusrtcp.dylib
0x00000001da76b000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x00000002765ca000 	/usr/lib/swift/libswiftDistributed.dylib
0x0000000276663000 	/usr/lib/swift/libswiftSynchronization.dylib
0x00000001934f5000 	/usr/lib/libnetwork.dylib
0x00000001c7991000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000019bd54000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001a5849000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001d6437000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00000001a5825000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000027530c000 	/usr/lib/libAppleArchive.dylib
0x000000019b663000 	/usr/lib/libbz2.1.0.dylib
0x000000019d151000 	/usr/lib/liblzma.5.dylib
0x000000019ac5f000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001b6cee000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000019d33f000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000018ea0c000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000024b211000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001a534b000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000019ae97000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000019aff9000 	/usr/lib/libgermantok.dylib
0x000000019a086000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00000001952df000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001a5418000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000019ae88000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001a7068000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000019913a000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x0000000193970000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001aac03000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000019dc2a000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000018ff56000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x0000000198fde000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x0000000198d39000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00000001972e4000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000019bdba000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000025b239000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000019dc72000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x0000000199f60000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000019d172000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000019d1f3000 	/usr/lib/libutil.dylib
0x00000002660f8000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000019667d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00000001a3596000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000019d22b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000018e378000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000019f98c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000019479f000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000019e248000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000019fd7f000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000019fd76000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000019f95e000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000019ac2c000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00000002422ea000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000019d778000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x0000000196e61000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00000002759cd000 	/usr/lib/libhvf.dylib
0x0000000257147000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x0000000276613000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000027679b000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000019daf3000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000019d594000 	/usr/lib/libexpat.1.dylib
0x000000019e11e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000019e149000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000019e233000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000019db39000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000019e1da000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000019e1d1000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000246fb8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00000002423b5000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001ee273000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x0000000242f2a000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00000002422eb000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00000001a8e69000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x000000025a3a8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000019d76c000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001ee2d1000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001ee295000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001ee45d000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001ee29e000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001ee292000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001ee27b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x0000000199ea4000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000019b5c2000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000019b011000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000019b448000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000019b25d000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000019b47a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001f1b68000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001f1b4a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000018e1f3000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001bb9ce000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001c9147000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001b6d55000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000019e205000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000019fd0a000 	/usr/lib/libcups.2.dylib
0x000000019fda4000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000019fa0a000 	/usr/lib/libresolv.9.dylib
0x000000019bbd1000 	/usr/lib/libiconv.2.dylib
0x000000019d7ca000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001a7c77000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000019d5af000 	/usr/lib/libheimdal-asn1.dylib
0x0000000197147000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000019fe07000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x0000000197155000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000019bb2c000 	/usr/lib/libcharset.1.dylib
0x00000001ee7f9000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001b043d000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000024eb40000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000019f945000 	/usr/lib/libAudioStatistics.dylib
0x0000000198fb6000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x0000000190077000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001a9f8f000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000019f8e8000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00000001a121f000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000019d699000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x0000000268bb3000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000023c9ac000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001cda67000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001b6d59000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000019d7b9000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000019fd88000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001b5f12000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000019fc29000 	/usr/lib/libSMC.dylib
0x000000019e0e8000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000019fd96000 	/usr/lib/libperfcheck.dylib
0x0000000238842000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001da532000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001b6d20000 	/usr/lib/libmis.dylib
0x000000019f6f6000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000019e239000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001efe9a000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001a5956000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000019d493000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00000001a447d000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001a7c78000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000019acf0000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000023f950000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00000001a38b7000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001bf36a000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000027528a000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001b04f9000 	/usr/lib/libAccessibility.dylib
0x000000019ab3a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000019be98000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000019affc000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000019bd93000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000019be93000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000019a08d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000018eb19000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00000002502b1000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000019e1cc000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000019e1ac000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000019e1d4000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001da939000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001af718000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000026c3ef000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000019d54b000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001a54c5000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000019ff7a000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001a4665000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00000001a45b0000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000019fd69000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x0000000197345000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000019d5b9000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000019d336000 	/usr/lib/libIOReport.dylib
0x0000000238452000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000027564c000 	/usr/lib/libTLE.dylib
0x00000001eb2cf000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000019af99000 	/usr/lib/libmecab.dylib
0x000000018eca9000 	/usr/lib/libCRFSuite.dylib
0x0000000199f69000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000019bd4c000 	/usr/lib/libThaiTokenizer.dylib
0x000000019af63000 	/usr/lib/libCheckFix.dylib
0x0000000196588000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000024baf3000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x0000000191798000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001c9251000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000019d1f7000 	/usr/lib/libxslt.1.dylib
0x000000019af26000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000001a73eb000 	/usr/lib/libcurl.4.dylib
0x0000000275880000 	/usr/lib/libcrypto.46.dylib
0x00000002763a8000 	/usr/lib/libssl.48.dylib
0x00000001a70c4000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001a7100000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000019fa27000 	/usr/lib/libsasl2.2.dylib
0x00000001b418d000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001a3ee5000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001ead59000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001d6beb000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000025ccb1000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00000001a31b1000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000103e78000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/server/libjvm.dylib
0x00000001029b8000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjimage.dylib
0x0000000102a14000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjdwp.dylib
0x0000000102a5c000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libjava.dylib
0x00000001029e4000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libinstrument.dylib
0x0000000102af4000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libzip.dylib
0x000000027527c000 	/usr/lib/i18n/libiconv_std.dylib
0x0000000275272000 	/usr/lib/i18n/libUTF8.dylib
0x0000000275281000 	/usr/lib/i18n/libmapper_none.dylib
0x0000000102ae0000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libdt_socket.dylib
0x0000000102c90000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnio.dylib
0x0000000103e04000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libnet.dylib
0x0000000102c70000 	/Users/<USER>/.sdkman/candidates/java/21.0.6-zulu/zulu-21.jdk/Contents/Home/lib/libextnet.dylib


VM Arguments:
jvm_args: -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true --add-opens=java.base/sun.nio.ch=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=n,suspend=y,address=127.0.0.1:64378 -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2025.1/captureAgent/debugger-agent.jar=file:///var/folders/1q/nff07fl14ss1md1tvlg_hj540000gn/T/capture16584118917623686056.props -Dfile.encoding=UTF-8 -Duser.country=TW -Duser.language=en -Duser.variant 
java_command: org.example.App
java_class_path (initial): /Users/<USER>/Documents/Projects/simple-sequencer/exchange/build/classes/java/main:/Users/<USER>/Documents/Projects/simple-sequencer/exchange/build/resources/main:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.aeron/aeron-all/1.42.1/782f6e43148542cd0b122004a562d658403bc360/aeron-all-1.42.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/uk.co.real-logic/sbe-tool/1.30.0/b89953357e008b144321f607ad4606de47e0279a/sbe-tool-1.30.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.5.18/fc371f3fc97a639de2d67947cffb7518ec5e3d40/logback-classic-1.5.18.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-api/2.0.17/d9e58ac9c7779ba3bf8142aff6c830617a7fe60f/slf4j-api-2.0.17.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.agrona/agrona/1.20.0/580b67864f7739bf7778162f418ada69fa3037/agrona-1.20.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.5.18/6c0375624f6f36b4e089e2488ba21334a11ef13f/logback-core-1.5.18.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {ergonomic}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839564                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909338                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909338                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=/Users/<USER>/.sdkman/candidates/java/current
PATH=/Users/<USER>/.nvm/versions/node/v22.0.0/bin:/Users/<USER>/.sdkman/candidates/java/current/bin:/Users/<USER>/.sdkman/candidates/groovy/current/bin:/Users/<USER>/.sdkman/candidates/gradle/current/bin:/opt/homebrew/anaconda3/bin:/Users/<USER>/bin:/usr/local/bin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts
SHELL=/bin/zsh
LC_CTYPE=en_US.UTF-8
TMPDIR=/var/folders/1q/nff07fl14ss1md1tvlg_hj540000gn/T/

Active Locale:
LC_ALL=C/en_US.UTF-8/C/C/C/C
LC_COLLATE=C
LC_CTYPE=en_US.UTF-8
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T8112 arm64
OS uptime: 2 days 1:17 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 1048576/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 2.21 2.23 2.91

CPU: total 8 (initial active 8) 0x61:0x0:0xda33d83d:0, fp, asimd, aes, pmull, sha1, sha256, crc32, lse, sha3, sha512
machdep.cpu.brand_string:Apple M2
hw.cachelinesize:128
hw.l1icachesize:131072
hw.l1dcachesize:65536
hw.l2cachesize:4194304

Memory: 16k page, physical 16777216k(529424k free), swap 0k(0k free)

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for bsd-aarch64 JRE (21.0.6+7-LTS) (Zulu21.40+17-CA), built on 2025-01-07T13:00:40Z by "tester" with clang Apple LLVM 14.0.3 (clang-1403.0.22.14.1)

END.
